[2025-07-28 11:26:11] local.ERROR: Provider creation failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_number' in 'field list' (Connection: mysql, SQL: insert into `providers` (`commercial_name`, `commercial_register_no`, `institution_name`, `sponsor_name`, `sponsor_phone`, `nationality`, `residence_type`, `in_home`, `in_salon`, `comission`, `salon_type`, `id_number`, `user_id`, `status`, `updated_at`, `created_at`) values ({"ar":"<PERSON> B<PERSON>ks","en":"<PERSON><PERSON>"}, <PERSON>ui dolores est magn, <PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON>, 509873454, other, professional, 1, 0, 80, freelancer, ?, 46, accepted, 2025-07-28 11:26:11, 2025-07-28 11:26:11))  
[2025-07-28 11:27:03] local.ERROR: Command "add_id_number_to_providers_table" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"add_id_number_to_providers_table\" is not defined. at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('add_id_number_t...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-28 11:27:51] local.INFO: Working hours data received: {"working_hours":[]} 
[2025-07-28 11:27:51] local.INFO: No working hours data provided or empty array  
[2025-07-28 12:44:58] local.INFO: Electronic payment refund initiated {"order_id":2,"amount":22.75,"payment_reference":"WALLET-**********"} 
[2025-07-28 12:45:53] local.INFO: Product quantity reserved {"product_id":3,"product_name":"سبراي حماية الشعر من الحرارة","reserved_quantity":2,"remaining_quantity":20} 
[2025-07-28 12:45:53] local.INFO: Bank transfer payment submitted {"order_id":3,"order_number":"2862685","transfer_id":4,"transfer_amount":"100","sender_bank":"dasndnasdj"} 
[2025-07-28 12:45:53] local.INFO: Cart cleared after order creation {"cart_id":7,"payment_method":5} 
[2025-07-28 12:48:26] local.ERROR: Cannot modify header information - headers already sent by (output started at D:\Workstation\Taswk\sorriso-backend\vendor\mpdf\mpdf\src\Mpdf.php:9624) {"exception":"[object] (ErrorException(code: 0): Cannot modify header information - headers already sent by (output started at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\mpdf\\mpdf\\src\\Mpdf.php:9624) at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\Workstation\\\\...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\Workstation\\\\...', 322)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 200 OK', true, 200)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(58): Symfony\\Component\\HttpFoundation\\Response->send()
#5 {main}
"} 
[2025-07-28 12:48:29] local.ERROR: Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\Workstation\Taswk\sorriso-backend\vendor\mpdf\mpdf\src\Mpdf.php:9624) in D:\Workstation\Taswk\sorriso-backend\vendor\symfony\http-foundation\Response.php:322
Stack trace:
#0 D:\Workstation\Taswk\sorriso-backend\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\Workstation\\...', 322)
#1 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Cannot modify h...', 'D:\\Workstation\\...', 322)
#2 D:\Workstation\Taswk\sorriso-backend\vendor\symfony\http-foundation\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\Workstation\Taswk\sorriso-backend\vendor\symfony\http-foundation\Response.php(401): Symfony\Component\HttpFoundation\Response->sendHeaders()
#4 D:\Workstation\Taswk\sorriso-backend\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Symfony\Component\HttpFoundation\Response->send()
#5 D:\Workstation\Taswk\sorriso-backend\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(ErrorException))
#6 D:\Workstation\Taswk\sorriso-backend\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(ErrorException))
#7 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(ErrorException))
#8 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\mpdf\\mpdf\\src\\Mpdf.php:9624) in D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\Workstation\\\\...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\Workstation\\\\...', 322)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(ErrorException))
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(ErrorException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(ErrorException))
#8 {main}
  thrown at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 {main}
"} 
[2025-07-28 12:55:44] local.ERROR: Attempt to read property "created_at" on null {"view":{"view":"D:\\Workstation\\Taswk\\sorriso-backend\\resources\\views\\admin\\orderrates\\show.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-477914656 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#2075</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-477914656\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","orderrate":"<pre class=sf-dump id=sf-dump-1966780788 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\OrderRate</span> {<a class=sf-dump-ref>#2333</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">order_rates</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>quality_rate</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>timing_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>service_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"10 characters\">jwneqjwnej</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 11:19:48</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 11:21:18</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>quality_rate</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>timing_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>service_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"10 characters\">jwneqjwnej</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 11:19:48</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-07 11:21:18</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">order_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">quality_rate</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">timing_rate</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">service_rate</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
  +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1966780788\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","settings":"<pre class=sf-dump id=sf-dump-1313992621 data-indent-pad=\"  \"><span class=sf-dump-note>array:85</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>is_production</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>name_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>name_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sorriso</span>\"
  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>whatsapp</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://sorriso-backend.test/storage/68/1751971786_logo.png</span>\"
  \"<span class=sf-dump-key>fav_icon</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://sorriso-backend.test/storage/39/1751806435_fav_icon.jpg</span>\"
  \"<span class=sf-dump-key>no_data_icon</span>\" => \"<span class=sf-dump-str title=\"7 characters\">fav.png</span>\"
  \"<span class=sf-dump-key>default_user</span>\" => \"<span class=sf-dump-str title=\"60 characters\">http://sorriso-backend.test/storage/images/users/default.png</span>\"
  \"<span class=sf-dump-key>profile_cover</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>login_background</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_logo</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_loader</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_name_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_name_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sorriso</span>\"
  \"<span class=sf-dump-key>intro_about</span>\" => \"<span class=sf-dump-str title=\"407 characters\">&#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;</span>\"
  \"<span class=sf-dump-key>intro_about_ar</span>\" => \"<span class=sf-dump-str title=\"407 characters\">&#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;</span>\"
  \"<span class=sf-dump-key>intro_about_en</span>\" => \"<span class=sf-dump-str title=\"507 characters\">This text is an example of text that can be replaced in the same space. This text was generated from the Arabic text generator, where you can generate such text or many other texts. This text is an example of text that can be replaced in the same space. This text is an example of text It can be replaced in the same space. This text was generated from the Arabic text generator, where you can generate such text or many other texts. This text is an example of a text that can be replaced in the same space.</span>\"
  \"<span class=sf-dump-key>about_image_2</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>about_image_1</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>services_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>services_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>services_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>how_work_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>how_work_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>how_work_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>fqs_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>fqs_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>fqs_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>parteners_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>parteners_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>parteners_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>contact_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>contact_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>contact_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>intro_email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>intro_phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>intro_address</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1575;&#1604;&#1603;&#1608;&#1610;&#1578; &#8211; &#1588;&#1585;&#1602; &#8211; &#1576;&#1585;&#1580; &#1603;&#1610;&#1576;&#1603;&#1608;</span>\"
  \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#10163a</span>\"
  \"<span class=sf-dump-key>buttons_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#7367F0</span>\"
  \"<span class=sf-dump-key>hover_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#262c49</span>\"
  \"<span class=sf-dump-key>intro_meta_description</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1608;&#1602;&#1593; &#1578;&#1593;&#1585;&#1610;&#1601;&#1610; &#1582;&#1575;&#1589; &#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_meta_keywords</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1608;&#1602;&#1593; &#1578;&#1593;&#1585;&#1610;&#1601;&#1610; &#1582;&#1575;&#1589; &#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>smtp_user_name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">smtp_user_name</span>\"
  \"<span class=sf-dump-key>smtp_password</span>\" => \"<span class=sf-dump-str title=\"13 characters\">smtp_password</span>\"
  \"<span class=sf-dump-key>smtp_mail_from</span>\" => \"<span class=sf-dump-str title=\"14 characters\">smtp_mail_from</span>\"
  \"<span class=sf-dump-key>smtp_sender_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">smtp_sender_name</span>\"
  \"<span class=sf-dump-key>smtp_port</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"
  \"<span class=sf-dump-key>smtp_host</span>\" => \"<span class=sf-dump-str title=\"13 characters\">send.smtp.com</span>\"
  \"<span class=sf-dump-key>smtp_encryption</span>\" => \"<span class=sf-dump-str title=\"3 characters\">LTS</span>\"
  \"<span class=sf-dump-key>firebase_key</span>\" => \"\"
  \"<span class=sf-dump-key>firebase_sender_id</span>\" => \"\"
  \"<span class=sf-dump-key>google_places</span>\" => \"\"
  \"<span class=sf-dump-key>google_analytics</span>\" => \"\"
  \"<span class=sf-dump-key>live_chat</span>\" => \"\"
  \"<span class=sf-dump-key>default_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"
  \"<span class=sf-dump-key>locales</span>\" => \"<span class=sf-dump-str title=\"11 characters\">[&quot;ar&quot;,&quot;en&quot;]</span>\"
  \"<span class=sf-dump-key>rtl_locales</span>\" => \"<span class=sf-dump-str title=\"6 characters\">[&quot;ar&quot;]</span>\"
  \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>countries</span>\" => \"<span class=sf-dump-str title=\"5 characters\">[&quot;1&quot;]</span>\"
  \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">KWD</span>\"
  \"<span class=sf-dump-key>currencies</span>\" => \"<span class=sf-dump-str title=\"7 characters\">[&quot;KWD&quot;]</span>\"
  \"<span class=sf-dump-key>vat_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.15</span>\"
  \"<span class=sf-dump-key>registeration_availability</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>loyalty_points_enabled</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>loyalty_points_earn_rate</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>loyalty_points_redeem_rate</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.5</span>\"
  \"<span class=sf-dump-key>loyalty_points_min_redeem</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"
  \"<span class=sf-dump-key>loyalty_points_max_redeem_percentage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
  \"<span class=sf-dump-key>normal_delivery_fee</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"
  \"<span class=sf-dump-key>express_delivery_fee</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"
  \"<span class=sf-dump-key>salon_comission</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
  \"<span class=sf-dump-key>product_referral_commission</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>service_referral_commission</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>comission_withdrawal_fee</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>about_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1606; &#1606;&#1581;&#1606;</span>\"
  \"<span class=sf-dump-key>terms_ar</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1575;&#1604;&#1588;&#1585;&#1608;&#1591; &#1608;&#1575;&#1604;&#1575;&#1581;&#1603;&#1575;&#1605;</span>\"
  \"<span class=sf-dump-key>privacy_ar</span>\" => \"<span class=sf-dump-str title=\"264 characters\">&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;</span>\"
  \"<span class=sf-dump-key>cancel_policy_ar</span>\" => \"<span class=sf-dump-str title=\"28 characters\">&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1573;&#1604;&#1594;&#1575;&#1569; &#1576;&#1575;&#1604;&#1604;&#1594;&#1577; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;</span>\"
  \"<span class=sf-dump-key>about_en</span>\" => \"<span class=sf-dump-str title=\"5 characters\">about</span>\"
  \"<span class=sf-dump-key>terms_en</span>\" => \"<span class=sf-dump-str title=\"5 characters\">terms</span>\"
  \"<span class=sf-dump-key>privacy_en</span>\" => \"<span class=sf-dump-str title=\"264 characters\">Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;</span>\"
  \"<span class=sf-dump-key>cancel_policy_en</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Cancellation policy in english</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1313992621\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Attempt to read property \"created_at\" on null at D:\\Workstation\\Taswk\\sorriso-backend\\resources\\views\\admin\\orderrates\\show.blade.php:114)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Workstation\\\\...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Workstation\\\\...', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}

[previous exception] [object] (ErrorException(code: 0): Attempt to read property \"created_at\" on null at D:\\Workstation\\Taswk\\sorriso-backend\\storage\\framework\\views\\cd99b1cd2c9e05b94b6331492121048e.php:115)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\Workstation\\\\...', 115)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\storage\\framework\\views\\cd99b1cd2c9e05b94b6331492121048e.php(115): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\Workstation\\\\...', 115)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Workstation\\\\...')
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Workstation\\\\...', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}
"} 
[2025-07-28 12:56:23] local.ERROR: Order rating failed {"error":"يجب تسليم الطلب قبل إمكانية تقييمه","order_id":"1","user_id":28} 
[2025-07-28 12:56:45] local.ERROR: Order rating failed {"error":"يجب تسليم الطلب قبل إمكانية تقييمه","order_id":"1","user_id":28} 
[2025-07-28 13:01:13] local.ERROR: Method Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection::getRelated does not exist. {"view":{"view":"D:\\Workstation\\Taswk\\sorriso-backend\\resources\\views\\admin\\orderrates\\show.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-618356057 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#2075</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-618356057\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","orderrate":"<pre class=sf-dump id=sf-dump-206286120 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\OrderRate</span> {<a class=sf-dump-ref>#2333</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">order_rates</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>28</span>
    \"<span class=sf-dump-key>quality_rate</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>timing_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>service_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"10 characters\">jwneqjwnej</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-28 12:57:28</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-28 12:57:46</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>28</span>
    \"<span class=sf-dump-key>quality_rate</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>timing_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>service_rate</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>body</span>\" => \"<span class=sf-dump-str title=\"10 characters\">jwneqjwnej</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-28 12:57:28</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-28 12:57:46</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Order
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Order</span></span> {<a class=sf-dump-ref>#2305</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">orders</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:34</span> [ &#8230;34]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:34</span> [ &#8230;34]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:40</span> [ &#8230;40]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#2303</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [ &#8230;18]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
      +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
    </samp>}
    \"<span class=sf-dump-key>media</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MediaCollection</span></span> {<a class=sf-dump-ref>#2316</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">collectionName</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">formFieldName</span>: <span class=sf-dump-const>null</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">order_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">quality_rate</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">timing_rate</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">service_rate</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">body</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
  +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-206286120\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","settings":"<pre class=sf-dump id=sf-dump-867924699 data-indent-pad=\"  \"><span class=sf-dump-note>array:85</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>is_production</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>name_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>name_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sorriso</span>\"
  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>whatsapp</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://sorriso-backend.test/storage/68/1751971786_logo.png</span>\"
  \"<span class=sf-dump-key>fav_icon</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://sorriso-backend.test/storage/39/1751806435_fav_icon.jpg</span>\"
  \"<span class=sf-dump-key>no_data_icon</span>\" => \"<span class=sf-dump-str title=\"7 characters\">fav.png</span>\"
  \"<span class=sf-dump-key>default_user</span>\" => \"<span class=sf-dump-str title=\"60 characters\">http://sorriso-backend.test/storage/images/users/default.png</span>\"
  \"<span class=sf-dump-key>profile_cover</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>login_background</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_logo</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_loader</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_name_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_name_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sorriso</span>\"
  \"<span class=sf-dump-key>intro_about</span>\" => \"<span class=sf-dump-str title=\"407 characters\">&#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;</span>\"
  \"<span class=sf-dump-key>intro_about_ar</span>\" => \"<span class=sf-dump-str title=\"407 characters\">&#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;</span>\"
  \"<span class=sf-dump-key>intro_about_en</span>\" => \"<span class=sf-dump-str title=\"507 characters\">This text is an example of text that can be replaced in the same space. This text was generated from the Arabic text generator, where you can generate such text or many other texts. This text is an example of text that can be replaced in the same space. This text is an example of text It can be replaced in the same space. This text was generated from the Arabic text generator, where you can generate such text or many other texts. This text is an example of a text that can be replaced in the same space.</span>\"
  \"<span class=sf-dump-key>about_image_2</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>about_image_1</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>services_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>services_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>services_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>how_work_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>how_work_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>how_work_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>fqs_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>fqs_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>fqs_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>parteners_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>parteners_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>parteners_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>contact_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>contact_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>contact_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>intro_email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>intro_phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>intro_address</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1575;&#1604;&#1603;&#1608;&#1610;&#1578; &#8211; &#1588;&#1585;&#1602; &#8211; &#1576;&#1585;&#1580; &#1603;&#1610;&#1576;&#1603;&#1608;</span>\"
  \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#10163a</span>\"
  \"<span class=sf-dump-key>buttons_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#7367F0</span>\"
  \"<span class=sf-dump-key>hover_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#262c49</span>\"
  \"<span class=sf-dump-key>intro_meta_description</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1608;&#1602;&#1593; &#1578;&#1593;&#1585;&#1610;&#1601;&#1610; &#1582;&#1575;&#1589; &#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_meta_keywords</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1608;&#1602;&#1593; &#1578;&#1593;&#1585;&#1610;&#1601;&#1610; &#1582;&#1575;&#1589; &#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>smtp_user_name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">smtp_user_name</span>\"
  \"<span class=sf-dump-key>smtp_password</span>\" => \"<span class=sf-dump-str title=\"13 characters\">smtp_password</span>\"
  \"<span class=sf-dump-key>smtp_mail_from</span>\" => \"<span class=sf-dump-str title=\"14 characters\">smtp_mail_from</span>\"
  \"<span class=sf-dump-key>smtp_sender_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">smtp_sender_name</span>\"
  \"<span class=sf-dump-key>smtp_port</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"
  \"<span class=sf-dump-key>smtp_host</span>\" => \"<span class=sf-dump-str title=\"13 characters\">send.smtp.com</span>\"
  \"<span class=sf-dump-key>smtp_encryption</span>\" => \"<span class=sf-dump-str title=\"3 characters\">LTS</span>\"
  \"<span class=sf-dump-key>firebase_key</span>\" => \"\"
  \"<span class=sf-dump-key>firebase_sender_id</span>\" => \"\"
  \"<span class=sf-dump-key>google_places</span>\" => \"\"
  \"<span class=sf-dump-key>google_analytics</span>\" => \"\"
  \"<span class=sf-dump-key>live_chat</span>\" => \"\"
  \"<span class=sf-dump-key>default_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"
  \"<span class=sf-dump-key>locales</span>\" => \"<span class=sf-dump-str title=\"11 characters\">[&quot;ar&quot;,&quot;en&quot;]</span>\"
  \"<span class=sf-dump-key>rtl_locales</span>\" => \"<span class=sf-dump-str title=\"6 characters\">[&quot;ar&quot;]</span>\"
  \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>countries</span>\" => \"<span class=sf-dump-str title=\"5 characters\">[&quot;1&quot;]</span>\"
  \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">KWD</span>\"
  \"<span class=sf-dump-key>currencies</span>\" => \"<span class=sf-dump-str title=\"7 characters\">[&quot;KWD&quot;]</span>\"
  \"<span class=sf-dump-key>vat_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.15</span>\"
  \"<span class=sf-dump-key>registeration_availability</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>loyalty_points_enabled</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>loyalty_points_earn_rate</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>loyalty_points_redeem_rate</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.5</span>\"
  \"<span class=sf-dump-key>loyalty_points_min_redeem</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"
  \"<span class=sf-dump-key>loyalty_points_max_redeem_percentage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
  \"<span class=sf-dump-key>normal_delivery_fee</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"
  \"<span class=sf-dump-key>express_delivery_fee</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"
  \"<span class=sf-dump-key>salon_comission</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
  \"<span class=sf-dump-key>product_referral_commission</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>service_referral_commission</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>comission_withdrawal_fee</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>about_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1606; &#1606;&#1581;&#1606;</span>\"
  \"<span class=sf-dump-key>terms_ar</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1575;&#1604;&#1588;&#1585;&#1608;&#1591; &#1608;&#1575;&#1604;&#1575;&#1581;&#1603;&#1575;&#1605;</span>\"
  \"<span class=sf-dump-key>privacy_ar</span>\" => \"<span class=sf-dump-str title=\"264 characters\">&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;</span>\"
  \"<span class=sf-dump-key>cancel_policy_ar</span>\" => \"<span class=sf-dump-str title=\"28 characters\">&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1573;&#1604;&#1594;&#1575;&#1569; &#1576;&#1575;&#1604;&#1604;&#1594;&#1577; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;</span>\"
  \"<span class=sf-dump-key>about_en</span>\" => \"<span class=sf-dump-str title=\"5 characters\">about</span>\"
  \"<span class=sf-dump-key>terms_en</span>\" => \"<span class=sf-dump-str title=\"5 characters\">terms</span>\"
  \"<span class=sf-dump-key>privacy_en</span>\" => \"<span class=sf-dump-str title=\"264 characters\">Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;</span>\"
  \"<span class=sf-dump-key>cancel_policy_en</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Cancellation policy in english</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-867924699\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Method Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection::getRelated does not exist. at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(60): Illuminate\\Support\\Collection->__call('getRelated', Array)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(162): Illuminate\\Database\\Eloquent\\Builder->has(Object(Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection), '>=', 1, 'and', Object(Closure))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\resources\\views\\admin\\orderrates\\show.blade.php(244): Illuminate\\Database\\Eloquent\\Builder->whereHas('getMedia', Object(Closure))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Workstation\\\\...')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Workstation\\\\...', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}

[previous exception] [object] (BadMethodCallException(code: 0): Method Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection::getRelated does not exist. at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(60): Illuminate\\Support\\Collection->__call('getRelated', Array)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(162): Illuminate\\Database\\Eloquent\\Builder->has(Object(Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection), '>=', 1, 'and', Object(Closure))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\storage\\framework\\views\\cd99b1cd2c9e05b94b6331492121048e.php(248): Illuminate\\Database\\Eloquent\\Builder->whereHas('getMedia', Object(Closure))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Workstation\\\\...')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Workstation\\\\...', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}
"} 
[2025-07-28 14:48:47] local.ERROR: count(): Argument #1 ($value) must be of type Countable|array, null given {"exception":"[object] (TypeError(code: 0): count(): Argument #1 ($value) must be of type Countable|array, null given at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:1217)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(1217): count(NULL)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->whereIn('id', NULL)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2123): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'whereIn', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('whereIn', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereIn', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('whereIn', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\ProductController.php(108): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereIn', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\ProductController->deleteAll(Object(Illuminate\\Http\\Request))
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('deleteAll', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\ProductController), 'deleteAll')
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-07-28 14:49:02] local.ERROR: count(): Argument #1 ($value) must be of type Countable|array, null given {"exception":"[object] (TypeError(code: 0): count(): Argument #1 ($value) must be of type Countable|array, null given at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:1217)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(1217): count(NULL)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->whereIn('id', NULL)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2123): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'whereIn', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('whereIn', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereIn', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('whereIn', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\ProductController.php(108): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereIn', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\ProductController->deleteAll(Object(Illuminate\\Http\\Request))
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('deleteAll', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\ProductController), 'deleteAll')
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-07-28 14:55:56] local.ERROR: count(): Argument #1 ($value) must be of type Countable|array, null given {"exception":"[object] (TypeError(code: 0): count(): Argument #1 ($value) must be of type Countable|array, null given at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:1217)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(1217): count(NULL)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->whereIn('id', NULL)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2123): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'whereIn', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('whereIn', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereIn', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('whereIn', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\ProductController.php(123): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereIn', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\ProductController->deleteAll(Object(Illuminate\\Http\\Request))
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('deleteAll', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\ProductController), 'deleteAll')
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-07-28 14:59:56] local.ERROR: count(): Argument #1 ($value) must be of type Countable|array, null given {"exception":"[object] (TypeError(code: 0): count(): Argument #1 ($value) must be of type Countable|array, null given at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:1217)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(1217): count(NULL)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Query\\Builder->whereIn('id', NULL)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2123): Illuminate\\Database\\Eloquent\\Builder->forwardCallTo(Object(Illuminate\\Database\\Query\\Builder), 'whereIn', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('whereIn', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereIn', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('whereIn', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\ServiceController.php(111): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereIn', Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\ServiceController->deleteAll(Object(Illuminate\\Http\\Request))
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('deleteAll', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\ServiceController), 'deleteAll')
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 {main}
"} 
[2025-07-28 15:28:47] local.ERROR: Undefined constant App\Models\Image::IMAGEPATH {"view":{"view":"D:\\Workstation\\Taswk\\sorriso-backend\\resources\\views\\admin\\images\\show.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-313950012 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#2087</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-313950012\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","image":"<pre class=sf-dump id=sf-dump-329126975 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Image</span> {<a class=sf-dump-ref>#2102</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">images</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"43 characters\">{&quot;ar&quot;: &quot;Summer Evans&quot;, &quot;en&quot;: &quot;Dylan Ortiz&quot;}</span>\"
    \"<span class=sf-dump-key>link</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Culpa quos sequi sed</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-29 12:56:38</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-29 12:56:38</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"43 characters\">{&quot;ar&quot;: &quot;Summer Evans&quot;, &quot;en&quot;: &quot;Dylan Ortiz&quot;}</span>\"
    \"<span class=sf-dump-key>link</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Culpa quos sequi sed</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-29 12:56:38</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-29 12:56:38</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">link</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">translatable</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
  +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">translationLocale</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-329126975\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","settings":"<pre class=sf-dump id=sf-dump-111602499 data-indent-pad=\"  \"><span class=sf-dump-note>array:85</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>is_production</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>name_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>name_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sorriso</span>\"
  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>whatsapp</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://sorriso-backend.test/storage/68/1751971786_logo.png</span>\"
  \"<span class=sf-dump-key>fav_icon</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://sorriso-backend.test/storage/39/1751806435_fav_icon.jpg</span>\"
  \"<span class=sf-dump-key>no_data_icon</span>\" => \"<span class=sf-dump-str title=\"7 characters\">fav.png</span>\"
  \"<span class=sf-dump-key>default_user</span>\" => \"<span class=sf-dump-str title=\"60 characters\">http://sorriso-backend.test/storage/images/users/default.png</span>\"
  \"<span class=sf-dump-key>profile_cover</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>login_background</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_logo</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_loader</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>intro_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_name_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_name_en</span>\" => \"<span class=sf-dump-str title=\"7 characters\">sorriso</span>\"
  \"<span class=sf-dump-key>intro_about</span>\" => \"<span class=sf-dump-str title=\"407 characters\">&#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;</span>\"
  \"<span class=sf-dump-key>intro_about_ar</span>\" => \"<span class=sf-dump-str title=\"407 characters\">&#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;&#1548; &#1604;&#1602;&#1583; &#1578;&#1605; &#1578;&#1608;&#1604;&#1610;&#1583; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1605;&#1606; &#1605;&#1608;&#1604;&#1583; &#1575;&#1604;&#1606;&#1589; &#1575;&#1604;&#1593;&#1585;&#1576;&#1609;&#1548; &#1581;&#1610;&#1579; &#1610;&#1605;&#1603;&#1606;&#1603; &#1571;&#1606; &#1578;&#1608;&#1604;&#1583; &#1605;&#1579;&#1604; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1571;&#1608; &#1575;&#1604;&#1593;&#1583;&#1610;&#1583; &#1605;&#1606; &#1575;&#1604;&#1606;&#1589;&#1608;&#1589; &#1575;&#1604;&#1571;&#1582;&#1585;&#1609; &#1607;&#1584;&#1575; &#1575;&#1604;&#1606;&#1589; &#1607;&#1608; &#1605;&#1579;&#1575;&#1604; &#1604;&#1606;&#1589; &#1610;&#1605;&#1603;&#1606; &#1571;&#1606; &#1610;&#1587;&#1578;&#1576;&#1583;&#1604; &#1601;&#1610; &#1606;&#1601;&#1587; &#1575;&#1604;&#1605;&#1587;&#1575;&#1581;&#1577;</span>\"
  \"<span class=sf-dump-key>intro_about_en</span>\" => \"<span class=sf-dump-str title=\"507 characters\">This text is an example of text that can be replaced in the same space. This text was generated from the Arabic text generator, where you can generate such text or many other texts. This text is an example of text that can be replaced in the same space. This text is an example of text It can be replaced in the same space. This text was generated from the Arabic text generator, where you can generate such text or many other texts. This text is an example of a text that can be replaced in the same space.</span>\"
  \"<span class=sf-dump-key>about_image_2</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>about_image_1</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://sorriso-backend.test/storage/images/settings/default.png</span>\"
  \"<span class=sf-dump-key>services_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>services_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>services_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>how_work_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>how_work_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>how_work_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>fqs_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>fqs_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>fqs_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>parteners_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>parteners_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>parteners_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>contact_text_ar</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>contact_text_en</span>\" => \"<span class=sf-dump-str title=\"163 characters\">By building an intuitive product that simulates and facilitates the implementation of public service, the simple answer has been to provide users with three things</span>\"
  \"<span class=sf-dump-key>contact_text</span>\" => \"<span class=sf-dump-str title=\"108 characters\">&#1605;&#1606; &#1582;&#1604;&#1575;&#1604; &#1576;&#1606;&#1575;&#1569; &#1605;&#1606;&#1578;&#1580; &#1576;&#1583;&#1610;&#1607;&#1610; &#1610;&#1581;&#1575;&#1603;&#1610; &#1608;&#1610;&#1587;&#1607;&#1604; &#1578;&#1606;&#1601;&#1610;&#1584; &#1575;&#1604;&#1582;&#1583;&#1605;&#1577; &#1575;&#1604;&#1593;&#1575;&#1605;&#1577; &#1548; &#1603;&#1575;&#1606; &#1575;&#1604;&#1580;&#1608;&#1575;&#1576; &#1575;&#1604;&#1576;&#1587;&#1610;&#1591; &#1607;&#1608; &#1578;&#1586;&#1608;&#1610;&#1583; &#1575;&#1604;&#1605;&#1587;&#1578;&#1582;&#1583;&#1605;&#1610;&#1606; &#1576;&#1579;&#1604;&#1575;&#1579;&#1577; &#1571;&#1588;&#1610;&#1575;&#1569;</span>\"
  \"<span class=sf-dump-key>intro_email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>intro_phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+96594971095</span>\"
  \"<span class=sf-dump-key>intro_address</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1575;&#1604;&#1603;&#1608;&#1610;&#1578; &#8211; &#1588;&#1585;&#1602; &#8211; &#1576;&#1585;&#1580; &#1603;&#1610;&#1576;&#1603;&#1608;</span>\"
  \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#10163a</span>\"
  \"<span class=sf-dump-key>buttons_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#7367F0</span>\"
  \"<span class=sf-dump-key>hover_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#262c49</span>\"
  \"<span class=sf-dump-key>intro_meta_description</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1608;&#1602;&#1593; &#1578;&#1593;&#1585;&#1610;&#1601;&#1610; &#1582;&#1575;&#1589; &#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>intro_meta_keywords</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1605;&#1608;&#1602;&#1593; &#1578;&#1593;&#1585;&#1610;&#1601;&#1610; &#1582;&#1575;&#1589; &#1586;&#1608;&#1585;&#1610;&#1587;&#1608;</span>\"
  \"<span class=sf-dump-key>smtp_user_name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">smtp_user_name</span>\"
  \"<span class=sf-dump-key>smtp_password</span>\" => \"<span class=sf-dump-str title=\"13 characters\">smtp_password</span>\"
  \"<span class=sf-dump-key>smtp_mail_from</span>\" => \"<span class=sf-dump-str title=\"14 characters\">smtp_mail_from</span>\"
  \"<span class=sf-dump-key>smtp_sender_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">smtp_sender_name</span>\"
  \"<span class=sf-dump-key>smtp_port</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"
  \"<span class=sf-dump-key>smtp_host</span>\" => \"<span class=sf-dump-str title=\"13 characters\">send.smtp.com</span>\"
  \"<span class=sf-dump-key>smtp_encryption</span>\" => \"<span class=sf-dump-str title=\"3 characters\">LTS</span>\"
  \"<span class=sf-dump-key>firebase_key</span>\" => \"\"
  \"<span class=sf-dump-key>firebase_sender_id</span>\" => \"\"
  \"<span class=sf-dump-key>google_places</span>\" => \"\"
  \"<span class=sf-dump-key>google_analytics</span>\" => \"\"
  \"<span class=sf-dump-key>live_chat</span>\" => \"\"
  \"<span class=sf-dump-key>default_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"
  \"<span class=sf-dump-key>locales</span>\" => \"<span class=sf-dump-str title=\"11 characters\">[&quot;ar&quot;,&quot;en&quot;]</span>\"
  \"<span class=sf-dump-key>rtl_locales</span>\" => \"<span class=sf-dump-str title=\"6 characters\">[&quot;ar&quot;]</span>\"
  \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>countries</span>\" => \"<span class=sf-dump-str title=\"5 characters\">[&quot;1&quot;]</span>\"
  \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">KWD</span>\"
  \"<span class=sf-dump-key>currencies</span>\" => \"<span class=sf-dump-str title=\"7 characters\">[&quot;KWD&quot;]</span>\"
  \"<span class=sf-dump-key>vat_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.15</span>\"
  \"<span class=sf-dump-key>registeration_availability</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>loyalty_points_enabled</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>loyalty_points_earn_rate</span>\" => \"<span class=sf-dump-str>1</span>\"
  \"<span class=sf-dump-key>loyalty_points_redeem_rate</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.5</span>\"
  \"<span class=sf-dump-key>loyalty_points_min_redeem</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"
  \"<span class=sf-dump-key>loyalty_points_max_redeem_percentage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
  \"<span class=sf-dump-key>normal_delivery_fee</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"
  \"<span class=sf-dump-key>express_delivery_fee</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15.00</span>\"
  \"<span class=sf-dump-key>salon_comission</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
  \"<span class=sf-dump-key>product_referral_commission</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>service_referral_commission</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>comission_withdrawal_fee</span>\" => \"<span class=sf-dump-str>0</span>\"
  \"<span class=sf-dump-key>about_ar</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1605;&#1606; &#1606;&#1581;&#1606;</span>\"
  \"<span class=sf-dump-key>terms_ar</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1575;&#1604;&#1588;&#1585;&#1608;&#1591; &#1608;&#1575;&#1604;&#1575;&#1581;&#1603;&#1575;&#1605;</span>\"
  \"<span class=sf-dump-key>privacy_ar</span>\" => \"<span class=sf-dump-str title=\"264 characters\">&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1582;&#1589;&#1608;&#1589;&#1610;&#1577; &#1576;&#1575;&#1604;&#1604;&#1594;&#1607; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;&lt;br&gt;</span>\"
  \"<span class=sf-dump-key>cancel_policy_ar</span>\" => \"<span class=sf-dump-str title=\"28 characters\">&#1587;&#1610;&#1575;&#1587;&#1577; &#1575;&#1604;&#1573;&#1604;&#1594;&#1575;&#1569; &#1576;&#1575;&#1604;&#1604;&#1594;&#1577; &#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;</span>\"
  \"<span class=sf-dump-key>about_en</span>\" => \"<span class=sf-dump-str title=\"5 characters\">about</span>\"
  \"<span class=sf-dump-key>terms_en</span>\" => \"<span class=sf-dump-str title=\"5 characters\">terms</span>\"
  \"<span class=sf-dump-key>privacy_en</span>\" => \"<span class=sf-dump-str title=\"264 characters\">Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;Privacy in english&lt;br&gt;</span>\"
  \"<span class=sf-dump-key>cancel_policy_en</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Cancellation policy in english</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-111602499\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined constant App\\Models\\Image::IMAGEPATH at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\BaseModel.php:41)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(700): App\\Models\\BaseModel->getImageAttribute(NULL)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(2224): Illuminate\\Database\\Eloquent\\Model->mutateAttribute('image', NULL)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(520): Illuminate\\Database\\Eloquent\\Model->transformModelValue('image', NULL)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php(41): Illuminate\\Database\\Eloquent\\Model->getAttributeValue('image')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(474): App\\Models\\Image->getAttributeValue('image')
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2260): Illuminate\\Database\\Eloquent\\Model->getAttribute('image')
#6 D:\\Workstation\\Taswk\\sorriso-backend\\resources\\views\\admin\\images\\show.blade.php(19): Illuminate\\Database\\Eloquent\\Model->__get('image')
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Workstation\\\\...')
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Workstation\\\\...', Array)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}

[previous exception] [object] (Error(code: 0): Undefined constant App\\Models\\Image::IMAGEPATH at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\BaseModel.php:41)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(700): App\\Models\\BaseModel->getImageAttribute(NULL)
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(2224): Illuminate\\Database\\Eloquent\\Model->mutateAttribute('image', NULL)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(520): Illuminate\\Database\\Eloquent\\Model->transformModelValue('image', NULL)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php(41): Illuminate\\Database\\Eloquent\\Model->getAttributeValue('image')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(474): App\\Models\\Image->getAttributeValue('image')
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2260): Illuminate\\Database\\Eloquent\\Model->getAttribute('image')
#6 D:\\Workstation\\Taswk\\sorriso-backend\\storage\\framework\\views\\cfa7f0a9dc4b81ce01f6d0cf8b48a46a.php(17): Illuminate\\Database\\Eloquent\\Model->__get('image')
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\Workstation\\\\...')
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\Workstation\\\\...', Array)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\Workstation\\\\...', Array)
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\Workstation\\\\...', Array)
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-07-28 15:58:30] local.ERROR: syntax error, unexpected single-quoted string ",", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected single-quoted string \",\", expecting \"]\" at D:\\Workstation\\Taswk\\sorriso-backend\\routes\\web.php:1217)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(513): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\Workstation\\\\...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes('D:\\\\Workstation\\\\...')
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(207): Illuminate\\Routing\\Router->group(Array, 'D:\\\\Workstation\\\\...')
#3 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Providers\\RouteServiceProvider.php(71): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\Workstation\\\\...')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Providers\\RouteServiceProvider.php(49): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(168): Illuminate\\Container\\Container->call(Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(144): Illuminate\\Container\\Container->call(Object(Closure))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1154): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 'App\\\\Providers\\\\R...')
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#26 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-07-28 15:58:43] local.ERROR: syntax error, unexpected single-quoted string ",", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected single-quoted string \",\", expecting \"]\" at D:\\Workstation\\Taswk\\sorriso-backend\\routes\\web.php:1217)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(513): Illuminate\\Routing\\RouteFileRegistrar->register('D:\\\\Workstation\\\\...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes('D:\\\\Workstation\\\\...')
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(207): Illuminate\\Routing\\Router->group(Array, 'D:\\\\Workstation\\\\...')
#3 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Providers\\RouteServiceProvider.php(71): Illuminate\\Routing\\RouteRegistrar->group('D:\\\\Workstation\\\\...')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Providers\\RouteServiceProvider.php(49): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(168): Illuminate\\Container\\Container->call(Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(144): Illuminate\\Container\\Container->call(Object(Closure))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1154): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 'App\\\\Providers\\\\R...')
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#26 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-07-28 16:33:02] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`sorriso`.`blogs`, CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`)) (Connection: mysql, SQL: delete from `blog_categories` where `id` = 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`sorriso`.`blogs`, CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`)) (Connection: mysql, SQL: delete from `blog_categories` where `id` = 1) at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('delete from `bl...', Array, Object(Closure))
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('delete from `bl...', Array, Object(Closure))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(548): Illuminate\\Database\\Connection->affectingStatement('delete from `bl...', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4024): Illuminate\\Database\\Connection->delete('delete from `bl...', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1387): Illuminate\\Database\\Query\\Builder->delete()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1501): Illuminate\\Database\\Eloquent\\Builder->delete()
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1433): Illuminate\\Database\\Eloquent\\Model->performDeleteOnModel()
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): Illuminate\\Database\\Eloquent\\Model->delete()
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(261): Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Models\\BlogCategory), 0)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->each(Object(Closure))
#10 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\BlogCategoryController.php(76): Illuminate\\Support\\HigherOrderCollectionProxy->__call('delete', Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BlogCategoryController->destroyAll(Object(Illuminate\\Http\\Request))
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('destroyAll', Array)
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\BlogCategoryController), 'destroyAll')
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`sorriso`.`blogs`, CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`)) at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:596)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(596): PDOStatement->execute()
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('delete from `bl...', Array)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('delete from `bl...', Array, Object(Closure))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('delete from `bl...', Array, Object(Closure))
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(548): Illuminate\\Database\\Connection->affectingStatement('delete from `bl...', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4024): Illuminate\\Database\\Connection->delete('delete from `bl...', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1387): Illuminate\\Database\\Query\\Builder->delete()
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1501): Illuminate\\Database\\Eloquent\\Builder->delete()
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1433): Illuminate\\Database\\Eloquent\\Model->performDeleteOnModel()
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): Illuminate\\Database\\Eloquent\\Model->delete()
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(261): Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Models\\BlogCategory), 0)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->each(Object(Closure))
#12 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\BlogCategoryController.php(76): Illuminate\\Support\\HigherOrderCollectionProxy->__call('delete', Array)
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BlogCategoryController->destroyAll(Object(Illuminate\\Http\\Request))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('destroyAll', Array)
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\BlogCategoryController), 'destroyAll')
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 {main}
"} 
[2025-07-28 16:33:44] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`sorriso`.`blogs`, CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`)) (Connection: mysql, SQL: delete from `blog_categories` where `id` = 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`sorriso`.`blogs`, CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`)) (Connection: mysql, SQL: delete from `blog_categories` where `id` = 1) at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('delete from `bl...', Array, Object(Closure))
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('delete from `bl...', Array, Object(Closure))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(548): Illuminate\\Database\\Connection->affectingStatement('delete from `bl...', Array)
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4024): Illuminate\\Database\\Connection->delete('delete from `bl...', Array)
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1387): Illuminate\\Database\\Query\\Builder->delete()
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1501): Illuminate\\Database\\Eloquent\\Builder->delete()
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1433): Illuminate\\Database\\Eloquent\\Model->performDeleteOnModel()
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): Illuminate\\Database\\Eloquent\\Model->delete()
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(261): Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Models\\BlogCategory), 0)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->each(Object(Closure))
#10 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\BlogCategoryController.php(76): Illuminate\\Support\\HigherOrderCollectionProxy->__call('delete', Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BlogCategoryController->destroyAll(Object(Illuminate\\Http\\Request))
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('destroyAll', Array)
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\BlogCategoryController), 'destroyAll')
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1451 Cannot delete or update a parent row: a foreign key constraint fails (`sorriso`.`blogs`, CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `blog_categories` (`id`)) at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:596)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(596): PDOStatement->execute()
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('delete from `bl...', Array)
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('delete from `bl...', Array, Object(Closure))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('delete from `bl...', Array, Object(Closure))
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(548): Illuminate\\Database\\Connection->affectingStatement('delete from `bl...', Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4024): Illuminate\\Database\\Connection->delete('delete from `bl...', Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1387): Illuminate\\Database\\Query\\Builder->delete()
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1501): Illuminate\\Database\\Eloquent\\Builder->delete()
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1433): Illuminate\\Database\\Eloquent\\Model->performDeleteOnModel()
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(65): Illuminate\\Database\\Eloquent\\Model->delete()
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(261): Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Models\\BlogCategory), 0)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(64): Illuminate\\Support\\Collection->each(Object(Closure))
#12 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Controllers\\Admin\\BlogCategoryController.php(76): Illuminate\\Support\\HigherOrderCollectionProxy->__call('delete', Array)
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\BlogCategoryController->destroyAll(Object(Illuminate\\Http\\Request))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('destroyAll', Array)
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\BlogCategoryController), 'destroyAll')
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminLang.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\CheckRoleMiddleware.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\CheckRoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\Admin\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\Admin\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\WebCors.php(9): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\WebCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\app\\Http\\Middleware\\SiteLang.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SiteLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 D:\\Workstation\\Taswk\\sorriso-backend\\public\\index.php(54): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 {main}
"} 
