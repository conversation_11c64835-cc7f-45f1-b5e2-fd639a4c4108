[2025-07-28 11:26:11] local.ERROR: Provider creation failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_number' in 'field list' (Connection: mysql, SQL: insert into `providers` (`commercial_name`, `commercial_register_no`, `institution_name`, `sponsor_name`, `sponsor_phone`, `nationality`, `residence_type`, `in_home`, `in_salon`, `comission`, `salon_type`, `id_number`, `user_id`, `status`, `updated_at`, `created_at`) values ({"ar":"<PERSON> B<PERSON>ks","en":"<PERSON><PERSON>"}, <PERSON>ui dolores est magn, <PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON>, 509873454, other, professional, 1, 0, 80, freelancer, ?, 46, accepted, 2025-07-28 11:26:11, 2025-07-28 11:26:11))  
[2025-07-28 11:27:03] local.ERROR: Command "add_id_number_to_providers_table" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"add_id_number_to_providers_table\" is not defined. at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('add_id_number_t...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-28 11:27:51] local.INFO: Working hours data received: {"working_hours":[]} 
[2025-07-28 11:27:51] local.INFO: No working hours data provided or empty array  
