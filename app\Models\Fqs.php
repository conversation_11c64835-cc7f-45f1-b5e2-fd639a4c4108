<?php

namespace App\Models;

use Spatie\Translatable\HasTranslations;

class Fqs extends BaseModel
{
    use HasTranslations;
    protected $fillable = ['question','answer' , 'audience_type'];
    public $translatable = ['question','answer'];

    public function scopeSearch($query, $searchArray = [])
    {
        $searchArray = is_array($searchArray) ? $searchArray : [];

        $query->where(function ($q) use ($searchArray) {
            foreach ($searchArray as $key => $value) {
                if (is_null($value) || $value === '') continue;

                if (str_contains($key, '_id')) {
                    $q->where($key, $value);
                } elseif ($key === 'audience_type') {
                    // Exact match for audience_type
                    $q->where($key, $value);
                } elseif ($key === 'created_at_min') {
                    $q->whereDate('created_at', '>=', $value);
                } elseif ($key === 'created_at_max') {
                    $q->whereDate('created_at', '<=', $value);
                } elseif ($key !== 'order') {
                    // For translatable fields, search in JSON
                    if (in_array($key, $this->translatable)) {
                        $q->where(function($subQuery) use ($key, $value) {
                            $subQuery->where($key . '->ar', 'like', "%{$value}%")
                                    ->orWhere($key . '->en', 'like', "%{$value}%");
                        });
                    } else {
                        $q->where($key, 'like', "%{$value}%");
                    }
                }
            }
        });

        $orderDirection = isset($searchArray['order']) ? $searchArray['order'] : 'DESC';

        return $query->orderBy('created_at', $orderDirection);
    }
}
