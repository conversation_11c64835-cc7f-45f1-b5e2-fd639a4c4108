<div class="position-relative">
    {{-- table loader  --}}
    {{-- <div class="table_loader" >
        {{__('admin.loading')}}
    </div> --}}
    {{-- table loader  --}}

    {{-- table content --}}
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>{{__('admin.image')}}</th>
                <th>{{__('admin.name')}}</th>
                <th>{{__('admin.status')}}</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($paymentmethods as $paymentmethod)
                <tr class="delete_row">
                    <td><img src="{{$paymentmethod->getFirstMediaUrl('payment-methods')}}" width="30px" height="30px" alt=""></td>
                    <td>{{ $paymentmethod->name }}</td>
                    <td>
                        {!! toggleBooleanView($paymentmethod, route('admin.model.active', ['model' => 'PaymentMethod', 'id' => $paymentmethod->id, 'action' => 'is_active'])) !!}
                    </td>

                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}
    {{-- no data found div --}}
    @if ($paymentmethods->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{asset('admin/app-assets/images/pages/404.png')}}" alt="">
            <span class="mt-2" style="font-family: cairo">{{__('admin.there_are_no_matches_matching')}}</span>
        </div>
    @endif
    {{-- no data found div --}}

</div>
{{-- pagination  links div --}}
@if ($paymentmethods->count() > 0 && $paymentmethods instanceof \Illuminate\Pagination\AbstractPaginator )
    <div class="d-flex justify-content-center mt-3">
        {{$paymentmethods->links()}}
    </div>
@endif
{{-- pagination  links div --}}

