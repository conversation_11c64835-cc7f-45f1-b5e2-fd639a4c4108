
<div class="position-relative">
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.serial_number')); ?></th>
                <th><?php echo e(__('admin.enrollment_id')); ?></th>
                <th><?php echo e(__('admin.enrollment_datetime')); ?></th>
                <th><?php echo e(__('admin.course_name')); ?></th>
                <th><?php echo e(__('admin.course')); ?> ID</th>
                <th><?php echo e(__('admin.course_provider')); ?></th>
                <th><?php echo e(__('admin.client_name')); ?></th>
                <th><?php echo e(__('admin.mobile_number')); ?></th>
                <th><?php echo e(__('admin.amount_paid')); ?></th>
                <th><?php echo e(__('admin.payment_method')); ?></th>
                <th><?php echo e(__('admin.payment_reference')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $enrollments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="<?php echo e($enrollment->id); ?>">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($enrollments->firstItem() + $index); ?></td>
                    <td><?php echo e($enrollment->id); ?></td>
                    <td><?php echo e($enrollment->enrolled_at ? $enrollment->enrolled_at->format('Y-m-d H:i:s') : '-'); ?></td>
                    <td><?php echo e($enrollment->course->name ?? '-'); ?></td>
                    <td><?php echo e($enrollment->course_id); ?></td>
                    <td><?php echo e($enrollment->course->instructor_name ?? '-'); ?></td>
                    <td><?php echo e($enrollment->user->name ?? '-'); ?></td>
                    <td><?php echo e($enrollment->user->phone ?? '-'); ?></td>
                    <td><?php echo e(number_format($enrollment->amount_paid, 2)); ?> <?php echo e(__('admin.riyal')); ?></td>
                    <td>
                        <?php switch($enrollment->payment_method_id):
                            case (1): ?>
                                <span class="badge badge-info">محفظة</span>
                                <?php break; ?>
                            <?php case (5): ?>
                                <span class="badge badge-warning">تحويل بنكي</span>
                                <?php break; ?>
                            <?php case (2): ?>
                                <span class="badge badge-primary">بطاقة ائتمان</span>
                                <?php break; ?>
                            <?php case (3): ?>
                                <span class="badge badge-success">مدى</span>
                                <?php break; ?>
                                    <?php case (4): ?>
                                <span class="badge badge-dark">Apple Pay</span>
                                <?php break; ?>
                            <?php default: ?>
                                <span class="badge badge-secondary"><?php echo e($enrollment->payment_method); ?></span>
                        <?php endswitch; ?>
                    </td>
                    <td><?php echo e($enrollment->payment_reference ?? '-'); ?></td>
                    <td class="product-action">
                        <span class="text-primary">
                            <a href="<?php echo e(route('admin.course_enrollments.show', ['id' => $enrollment->id])); ?>"
                               class="btn btn-warning btn-sm">
                                <i class="feather icon-eye"></i> <?php echo e(__('admin.show')); ?>

                            </a>
                        </span>

                        
                        

                        <span class="delete-row btn btn-danger btn-sm"
                              data-url="<?php echo e(url('admin/course-enrollments/' . $enrollment->id)); ?>">
                            <i class="feather icon-trash"></i><?php echo e(__('admin.delete')); ?>

                        </span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</div>



<?php if($enrollments->count() == 0): ?>
    <div class="d-flex flex-column w-100 align-center mt-4">
        <i class="la la-graduation-cap" style="font-size: 100px; color: #ddd;"></i>
        <h4 class="mt-3 text-muted"><?php echo e(__('admin.no_data_found')); ?></h4>
        <p class="text-muted">لا توجد اشتراكات في الدورات التدريبية</p>
    </div>
<?php endif; ?>



<div class="d-flex justify-content-center mt-3">
    <?php echo e($enrollments->appends(request()->query())->links()); ?>

</div>

<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/course_enrollments/table.blade.php ENDPATH**/ ?>