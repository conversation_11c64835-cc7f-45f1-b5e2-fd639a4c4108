

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Provider Details Section -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card mb-2">
                <div class="card-body text-center">
                    <h5 class="card-title"><?php echo e(__('admin.provider_suborders_total_sum')); ?></h5>
                    <h3 class="card-text">
                        <?php echo e(number_format($user->provider->providerSubOrders->sum('total'), 2)); ?> <?php echo e(__('admin.currency')); ?>

                    </h3>
                </div>
            </div>
           
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title"><?php echo e(__('admin.provider_details')); ?> - <?php echo e($user->name); ?></h4>
                    <div class="card-header-toolbar d-flex align-items-center">
                        <?php if($user->provider): ?>
                            <?php switch($user->provider->status):
                                case ('in_review'): ?>
                                    <span class="badge badge-info mr-2"><?php echo e(__('admin.in_review')); ?></span>
                                    <?php break; ?>
                                <?php case ('pending'): ?>
                                    <span class="badge badge-warning mr-2"><?php echo e(__('admin.pending')); ?></span>
                                    <?php break; ?>
                                <?php case ('accepted'): ?>
                                    <span class="badge badge-success mr-2"><?php echo e(__('admin.accepted')); ?></span>
                                    <?php break; ?>
                                <?php case ('rejected'): ?>
                                    <span class="badge badge-danger mr-2"><?php echo e(__('admin.rejected')); ?></span>
                                    <?php break; ?>
                                <?php case ('blocked'): ?>
                                    <span class="badge badge-dark mr-2"><?php echo e(__('admin.blocked')); ?></span>
                                    <?php break; ?>
                                <?php default: ?>
                                    <span class="badge badge-secondary mr-2"><?php echo e($user->provider->status); ?></span>
                            <?php endswitch; ?>
                        <?php endif; ?>

                        <a href="<?php echo e(route('admin.providers.index')); ?>" class="btn btn-primary btn-sm mr-1">
                            <i class="feather icon-arrow-left"></i> <?php echo e(__('admin.back')); ?>

                        </a>

                        <?php if($user->provider && $user->provider->status === 'in_review'): ?>
                            <button class="btn btn-success btn-sm mr-1 approve-provider" data-id="<?php echo e($user->id); ?>">
                                <i class="feather icon-check"></i> <?php echo e(__('admin.accept')); ?>

                            </button>
                            <button class="btn btn-danger btn-sm reject-provider" data-id="<?php echo e($user->id); ?>">
                                <i class="feather icon-x"></i> <?php echo e(__('admin.reject')); ?>

                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-content">
                    <div class="card-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="basic-info-tab" data-toggle="tab" href="#basic-info" aria-controls="basic-info" role="tab" aria-selected="true">
                                        <i class="feather icon-user"></i> <?php echo e(__('admin.basic_information')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="provider-info-tab" data-toggle="tab" href="#provider-info" aria-controls="provider-info" role="tab" aria-selected="false">
                                        <i class="feather icon-briefcase"></i> <?php echo e(__('admin.provider_information')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="documents-tab" data-toggle="tab" href="#documents" aria-controls="documents" role="tab" aria-selected="false">
                                        <i class="feather icon-file-text"></i> <?php echo e(__('admin.documents')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="services-tab" data-toggle="tab" href="#services" aria-controls="services" role="tab" aria-selected="false">
                                        <i class="feather icon-list"></i> <?php echo e(__('admin.services')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="products-tab" data-toggle="tab" href="#products" aria-controls="products" role="tab" aria-selected="false">
                                        <i class="feather icon-package"></i> <?php echo e(__('admin.products')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="orders-tab" data-toggle="tab" href="#orders" aria-controls="orders" role="tab" aria-selected="false">
                                        <i class="feather icon-shopping-cart"></i> <?php echo e(__('admin.orders')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="salon-images-tab" data-toggle="tab" href="#salon-images" aria-controls="salon-images" role="tab" aria-selected="false">
                                        <i class="feather icon-image"></i> <?php echo e(__('admin.past_work')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="working-hours-tab" data-toggle="tab" href="#working-hours" aria-controls="working-hours" role="tab" aria-selected="false">
                                        <i class="feather icon-clock"></i> <?php echo e(__('admin.working_hours')); ?>

                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="withdraw-request-tab" data-toggle="tab" href="#withdraw-request" aria-controls="working-hours" role="tab" aria-selected="false">
                                        <i class="feather icon-clock"></i> <?php echo e(__('admin.withdraw_request')); ?>

                                    </a>
                                </li>
                            </ul>

                            <!-- Tab panes -->
                            <div class="tab-content">
                                <!-- Basic Information Tab -->
                                <div class="tab-pane active" id="basic-info" aria-labelledby="basic-info-tab" role="tabpanel">
                                    <div class="row mt-2">
                                        <div class="col-md-3 text-center">
                                            <div class="avatar avatar-xl">
                                                <?php if($user->getFirstMediaUrl('profile')): ?>
                                                    <img src="<?php echo e($user->getFirstMediaUrl('profile')); ?>" alt="<?php echo e($user->name); ?>" class="round">
                                                <?php else: ?>
                                                    <img src="<?php echo e(asset('admin/app-assets/images/portrait/small/avatar-s-11.jpg')); ?>" alt="<?php echo e($user->name); ?>" class="round">
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-9">
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.name')); ?>:</strong></td>
                                                    <td><?php echo e($user->name); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.email')); ?>:</strong></td>
                                                    <td><?php echo e($user->email); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.phone')); ?>:</strong></td>
                                                    <td><?php echo e($user->full_phone); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.gender')); ?>:</strong></td>
                                                    <td><?php echo e($user->gender ? __('admin.' . $user->gender) : __('admin.not_set')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.city')); ?>:</strong></td>
                                                    <td><?php echo e($user->city->name ?? __('admin.not_set')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.region')); ?>:</strong></td>
                                                    <td><?php echo e($user->region->name ?? __('admin.not_set')); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.status')); ?>:</strong></td>
                                                    <td>
                                                        <?php if($user->provider): ?>
                                                        <?php switch($user->provider->status):
                                                            case ('in_review'): ?>
                                                                <span class="badge badge-info mr-2"><?php echo e(__('admin.in_review')); ?></span>
                                                                <?php break; ?>
                                                            <?php case ('pending'): ?>
                                                                <span class="badge badge-warning mr-2"><?php echo e(__('admin.pending')); ?></span>
                                                                <?php break; ?>
                                                            <?php case ('accepted'): ?>
                                                                <span class="badge badge-success mr-2"><?php echo e(__('admin.accepted')); ?></span>
                                                                <?php break; ?>
                                                            <?php case ('rejected'): ?>
                                                                <span class="badge badge-danger mr-2"><?php echo e(__('admin.rejected')); ?></span>
                                                                <?php break; ?>
                                                            <?php case ('blocked'): ?>
                                                                <span class="badge badge-dark mr-2"><?php echo e(__('admin.blocked')); ?></span>
                                                                <?php break; ?>
                                                            <?php default: ?>
                                                                <span class="badge badge-secondary mr-2"><?php echo e($user->provider->status); ?></span>
                                                        <?php endswitch; ?>
                                                    <?php endif; ?>                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong><?php echo e(__('admin.created_at')); ?>:</strong></td>
                                                    <td><?php echo e($user->created_at->format('Y-m-d H:i')); ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Provider Information Tab -->
                                <div class="tab-pane" id="provider-info" aria-labelledby="provider-info-tab" role="tabpanel">
                                    <?php if($user->provider): ?>
                                        <div class="row mt-2">
                                            <div class="col-md-6">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.commercial_name')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->commercial_name ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <?php if($user->provider->salon_type !== 'freelancer'): ?>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.commercial_register_no')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->commercial_register_no ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <?php endif; ?>

                                                    <?php if($user->provider->salon_type === 'freelancer' && $user->provider->nationality === 'saudi'): ?>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.id_number')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->id_number ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <?php endif; ?>
                                                    <?php if($user->provider->salon_type !== 'freelancer'): ?>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.institution_name')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->institution_name ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <?php endif; ?>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.salon_type')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->salon_type ? __('admin.' . $user->provider->salon_type) : __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.nationality')); ?>:</strong></td>
                                                        <td>
                                                            <?php echo e($user->provider->nationality ? __('admin.' . $user->provider->nationality) : __('admin.not_set')); ?>

                                                        </td>
                                                                                                            </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.sponsor_name')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->sponsor_name ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.sponsor_phone')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->sponsor_phone ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div class="col-md-6">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.is_mobile')); ?>:</strong></td>
                                                        <td>
                                                            <?php if($user->provider->is_mobile): ?>
                                                                <span class="badge badge-success"><?php echo e(__('admin.yes')); ?></span>
                                                            <?php else: ?>
                                                                <span class="badge badge-secondary"><?php echo e(__('admin.no')); ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.mobile_service_fee')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->mobile_service_fee ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.in_home')); ?>:</strong></td>
                                                        <td>
                                                            <?php if($user->provider->in_home): ?>
                                                                <span class="badge badge-success"><?php echo e(__('admin.yes')); ?></span>
                                                            <?php else: ?>
                                                                <span class="badge badge-secondary"><?php echo e(__('admin.no')); ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.in_salon')); ?>:</strong></td>
                                                        <td>
                                                            <?php if($user->provider->in_salon): ?>
                                                                <span class="badge badge-success"><?php echo e(__('admin.yes')); ?></span>
                                                            <?php else: ?>
                                                                <span class="badge badge-secondary"><?php echo e(__('admin.no')); ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.home_fees')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->home_fees ?? __('admin.not_set')); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.cuurent_worth_balance')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->wallet_balance ?? 0); ?> <?php echo e(__('admin.currency')); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong><?php echo e(__('admin.withdrawable_balance')); ?>:</strong></td>
                                                        <td><?php echo e($user->provider->withdrawable_balance ?? 0); ?> <?php echo e(__('admin.currency')); ?></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>

                                        <?php if($user->provider->description): ?>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <h5><?php echo e(__('admin.description')); ?></h5>
                                                    <p class="card-text"><?php echo e($user->provider->description); ?></p>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if($user->provider->rejection_reason): ?>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <div class="alert alert-danger">
                                                        <h5><?php echo e(__('admin.rejection_reason')); ?></h5>
                                                        <p class="mb-0"><?php echo e($user->provider->rejection_reason); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <?php echo e(__('admin.no_provider_information')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Salon Images Tab -->
                                <div class="tab-pane" id="salon-images" aria-labelledby="salon-images-tab" role="tabpanel">
                                    <?php if($user->provider): ?>
                                        <div class="row mt-2">
                                            
                                            <!-- Salon Images Section -->
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header bg-light-primary">
                                                        <h5><?php echo e(__('admin.past_work')); ?></h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <?php if($user->provider->salon_images_urls && $user->provider->salon_images_urls->count() > 0): ?>
                                                            <div class="row">
                                                                <?php $__currentLoopData = $user->provider->salon_images_urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $imageUrl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <div class="col-md-6 mb-3">
                                                                        <img src="<?php echo e($imageUrl); ?>" alt="<?php echo e(__('admin.salon_image')); ?>" class="img-fluid rounded" style="max-height: 150px; width: 100%; object-fit: cover;">
                                                                    </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </div>
                                                        <?php else: ?>
                                                            <p class="text-muted text-center"><?php echo e(__('admin.no_salon_images_uploaded')); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <?php echo e(__('admin.no_provider_information')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                                <!-- Documents Tab -->
                                <div class="tab-pane" id="documents" aria-labelledby="documents-tab" role="tabpanel">
                                    <?php if($user->provider): ?>
                                        <div class="row mt-2">
                                            <div class="col-md-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><?php echo e(__('admin.logo')); ?></h5>
                                                    </div>
                                                    <div class="card-body text-center">
                                                        <?php if($user->provider->getFirstMediaUrl('logo')): ?>
                                                            <img src="<?php echo e($user->provider->getFirstMediaUrl('logo')); ?>" alt="<?php echo e(__('admin.logo')); ?>" class="img-fluid" style="max-height: 200px;">
                                                        <?php else: ?>
                                                            <p class="text-muted"><?php echo e(__('admin.no_image_uploaded')); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php if($user->provider->salon_type !== 'freelancer'): ?>
                                            <div class="col-md-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><?php echo e(__('admin.commercial_register_image')); ?></h5>
                                                    </div>
                                                    <div class="card-body text-center">
                                                        <?php if($user->provider->getFirstMediaUrl('commercial_register_image')): ?>
                                                            <img src="<?php echo e($user->provider->getFirstMediaUrl('commercial_register_image')); ?>" alt="<?php echo e(__('admin.commercial_register_image')); ?>" class="img-fluid" style="max-height: 200px;">
                                                        <?php else: ?>
                                                            <p class="text-muted"><?php echo e(__('admin.no_image_uploaded')); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            <div class="col-md-4">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><?php echo e(__('admin.residence_image')); ?></h5>
                                                    </div>
                                                    <div class="card-body text-center">
                                                        <?php if($user->provider->getFirstMediaUrl('residence_image')): ?>
                                                            <img src="<?php echo e($user->provider->getFirstMediaUrl('residence_image')); ?>" alt="<?php echo e(__('admin.residence_image')); ?>" class="img-fluid" style="max-height: 200px;">
                                                        <?php else: ?>
                                                            <p class="text-muted"><?php echo e(__('admin.no_image_uploaded')); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Freelancer Specific Images -->
                                        <?php if($user->provider->salon_type === 'freelancer'): ?>
                                            <div class="row mt-2">
                                                <!-- Freelance Document Image -->
                                                <div class="col-md-4">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h5><?php echo e(__('admin.freelance_document_image')); ?></h5>
                                                        </div>
                                                        <div class="card-body text-center">
                                                            <?php if($user->provider->getFirstMediaUrl('freelance_document_image')): ?>
                                                                <img src="<?php echo e($user->provider->getFirstMediaUrl('freelance_document_image')); ?>" alt="<?php echo e(__('admin.freelance_document_image')); ?>" class="img-fluid" style="max-height: 200px;">
                                                            <?php else: ?>
                                                                <p class="text-muted"><?php echo e(__('admin.no_image_uploaded')); ?></p>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- ID Image for Saudi Freelancers -->
                                                <?php if($user->provider->nationality === 'saudi'): ?>
                                                <div class="col-md-4">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h5><?php echo e(__('admin.id_image')); ?></h5>
                                                        </div>
                                                        <div class="card-body text-center">
                                                            <?php if($user->provider->getFirstMediaUrl('id_image')): ?>
                                                                <img src="<?php echo e($user->provider->getFirstMediaUrl('id_image')); ?>" alt="<?php echo e(__('admin.id_image')); ?>" class="img-fluid" style="max-height: 200px;">
                                                            <?php else: ?>
                                                                <p class="text-muted"><?php echo e(__('admin.no_image_uploaded')); ?></p>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Past Work Images -->
                                        <?php if($user->provider->getMedia('salon_images')->count() > 0): ?>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <h5><?php echo e(__('admin.past_work')); ?></h5>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $user->provider->getMedia('salon_images'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-3 mb-2">
                                                                <img src="<?php echo e($image->getUrl()); ?>" alt="<?php echo e(__('admin.salon_image')); ?>" class="img-fluid rounded">
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <?php echo e(__('admin.no_provider_information')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Services Tab -->
                                <div class="tab-pane" id="services" aria-labelledby="services-tab" role="tabpanel">
                                    <?php if($user->provider && $user->provider->services && $user->provider->services->count() > 0): ?>
                                        <div class="table-responsive mt-2">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th><?php echo e(__('admin.name')); ?></th>
                                                        <th><?php echo e(__('admin.category')); ?></th>
                                                        <th><?php echo e(__('admin.price')); ?></th>
                                                        <th><?php echo e(__('admin.service_duration')); ?></th>
                                                        <th><?php echo e(__('admin.status')); ?></th>
                                                        <th><?php echo e(__('admin.created_at')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $user->provider->services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>

                                                            <td><?php echo e($service->name); ?></td>
                                                            <td><?php echo e($service->category->name ?? __('admin.not_set')); ?></td>
                                                            <td><?php echo e($service->price); ?> <?php echo e(__('admin.currency')); ?></td>
                                                            <td><?php echo e($service->duration); ?> <?php echo e(__('admin.minutes')); ?></td>
                                                            <td>
                                                                <?php if($service->is_active): ?>
                                                                    <span class="badge badge-success"><?php echo e(__('admin.active')); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-secondary"><?php echo e(__('admin.inactive')); ?></span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo e($service->created_at->format('Y-m-d')); ?></td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info mt-2">
                                            <i class="feather icon-info"></i> <?php echo e(__('admin.no_services_found')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Products Tab -->
                                <div class="tab-pane" id="products" aria-labelledby="products-tab" role="tabpanel">
                                    <?php if($user->provider && $user->provider->products && $user->provider->products->count() > 0): ?>
                                        <div class="table-responsive mt-2">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th><?php echo e(__('admin.image')); ?></th>
                                                        <th><?php echo e(__('admin.name')); ?></th>
                                                        <th><?php echo e(__('admin.category')); ?></th>
                                                        <th><?php echo e(__('admin.price')); ?></th>
                                                        <th><?php echo e(__('admin.stock_quantity')); ?></th>
                                                        <th><?php echo e(__('admin.status')); ?></th>
                                                        <th><?php echo e(__('admin.created_at')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $user->provider->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td>
                                                                <?php if($product->getFirstMediaUrl('image')): ?>
                                                                    <img src="<?php echo e($product->getFirstMediaUrl('image')); ?>" alt="<?php echo e($product->name); ?>" class="rounded" width="50" height="50">
                                                                <?php else: ?>
                                                                    <div class="avatar avatar-sm bg-light-secondary">
                                                                        <i class="feather icon-package"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo e($product->name); ?></td>
                                                            <td><?php echo e($product->category->name ?? __('admin.not_set')); ?></td>
                                                            <td><?php echo e($product->price); ?> <?php echo e(__('admin.currency')); ?></td>
                                                            <td>
                                                                <?php if($product->quantity > 0): ?>
                                                                    <span class="badge badge-success"><?php echo e($product->quantity); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-danger"><?php echo e(__('admin.out_of_stock')); ?></span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <?php if($product->is_active): ?>
                                                                    <span class="badge badge-success"><?php echo e(__('admin.active')); ?></span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-secondary"><?php echo e(__('admin.inactive')); ?></span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo e($product->created_at->format('Y-m-d')); ?></td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info mt-2">
                                            <i class="feather icon-info"></i> <?php echo e(__('admin.no_products_found')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Orders Tab -->
                                <div class="tab-pane" id="orders" aria-labelledby="orders-tab" role="tabpanel">
                                    <?php if($user->provider && $user->provider->providerSubOrders && $user->provider->providerSubOrders->count() > 0): ?>
                                        <div class="table-responsive mt-2">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th><?php echo e(__('admin.sub_order_number')); ?></th>
                                                        <th><?php echo e(__('admin.client')); ?></th>
                                                        <th><?php echo e(__('admin.total_amount')); ?></th>
                                                        <th><?php echo e(__('admin.status')); ?></th>
                                                        <th><?php echo e(__('admin.created_at')); ?></th>
                                                        <th><?php echo e(__('admin.actions')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $user->provider->providerSubOrders->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subOrder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td><?php echo e($subOrder->sub_order_number); ?></td>
                                                            <td><?php echo e($subOrder->order->user->name ?? __('admin.not_set')); ?></td>
                                                            <td><?php echo e($subOrder->total); ?> <?php echo e(__('admin.currency')); ?></td>
                                                            <td>
                                                                <?php switch($subOrder->status):
                                                                    case ('pending_payment'): ?>
                                                                        <span class="badge badge-warning"><?php echo e(__('admin.pending_payment')); ?></span>
                                                                        <?php break; ?>
                                                                    <?php case ('processing'): ?>
                                                                        <span class="badge badge-info"><?php echo e(__('admin.processing')); ?></span>
                                                                        <?php break; ?>
                                                                    <?php case ('confirmed'): ?>
                                                                        <span class="badge badge-primary"><?php echo e(__('admin.confirmed')); ?></span>
                                                                        <?php break; ?>
                                                                    <?php case ('completed'): ?>
                                                                        <span class="badge badge-success"><?php echo e(__('admin.completed')); ?></span>
                                                                        <?php break; ?>
                                                                    <?php case ('cancelled'): ?>
                                                                        <span class="badge badge-danger"><?php echo e(__('admin.cancelled')); ?></span>
                                                                        <?php break; ?>
                                                                    <?php default: ?>
                                                                        <span class="badge badge-secondary"><?php echo e($subOrder->status); ?></span>
                                                                <?php endswitch; ?>
                                                            </td>
                                                            <td><?php echo e($subOrder->created_at->format('Y-m-d H:i')); ?></td>
                                                            <td>
                                                                <a href="<?php echo e(route('admin.orders.show', $subOrder->order_id)); ?>" class="btn btn-sm btn-primary">
                                                                    <i class="feather icon-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        <?php if($user->provider->providerSubOrders->count() > 10): ?>
                                            <div class="text-center mt-2">
                                                <a href="<?php echo e(route('admin.orders.index', ['provider_id' => $user->provider->id])); ?>" class="btn btn-outline-primary">
                                                    <?php echo e(__('admin.view_all_orders')); ?>

                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <div class="alert alert-info mt-2">
                                            <i class="feather icon-info"></i> <?php echo e(__('admin.no_orders_found')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Working Hours Tab -->
                                <div class="tab-pane" id="working-hours" aria-labelledby="working-hours-tab" role="tabpanel">
                                    <?php if($user->provider && $user->provider->workingHours && $user->provider->workingHours->count() > 0): ?>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="table-responsive">
                                                    <table class="table table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th><?php echo e(__('admin.day')); ?></th>
                                                                <th><?php echo e(__('admin.status')); ?></th>
                                                                <th><?php echo e(__('admin.start_time')); ?></th>
                                                                <th><?php echo e(__('admin.end_time')); ?></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php
                                                                $days = [
                                                                    'sunday' => __('admin.sunday'),
                                                                    'monday' => __('admin.monday'),
                                                                    'tuesday' => __('admin.tuesday'),
                                                                    'wednesday' => __('admin.wednesday'),
                                                                    'thursday' => __('admin.thursday'),
                                                                    'friday' => __('admin.friday'),
                                                                    'saturday' => __('admin.saturday')
                                                                ];
                                                            ?>
                                                            <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dayKey => $dayName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <?php
                                                                    $workingHour = $user->provider->workingHours->where('day', $dayKey)->first();
                                                                ?>
                                                                <tr>
                                                                    <td><strong><?php echo e($dayName); ?></strong></td>
                                                                    <td>
                                                                        <?php if($workingHour && $workingHour->is_working): ?>
                                                                            <span class="badge badge-success"><?php echo e(__('admin.open')); ?></span>
                                                                        <?php else: ?>
                                                                            <span class="badge badge-danger"><?php echo e(__('admin.closed')); ?></span>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td>
                                                                        <?php if($workingHour && $workingHour->is_working): ?>
                                                                            <?php echo e($workingHour->start_time ?? __('admin.not_set')); ?>

                                                                        <?php else: ?>
                                                                            <span class="text-muted">-</span>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td>
                                                                        <?php if($workingHour && $workingHour->is_working): ?>
                                                                            <?php echo e($workingHour->end_time ?? __('admin.not_set')); ?>

                                                                        <?php else: ?>
                                                                            <span class="text-muted">-</span>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                </tr>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                    <?php else: ?>
                                        <div class="alert alert-info mt-2">
                                            <i class="feather icon-info"></i> <?php echo e(__('admin.no_working_hours_found')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>

                                 <div class="tab-pane" id="withdraw-request" aria-labelledby="withdraw-request-tab" role="tabpanel">
                                    <?php if($user->provider && $user->provider->withdrawRequests && $user->provider->withdrawRequests->count() > 0): ?>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="table-responsive">
                                                    <table class="table table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>#</th>
                                                                <th><?php echo e(__('admin.order_number')); ?></th>
                                                                <th><?php echo e(__('admin.provider')); ?></th>
                                                                <th><?php echo e(__('admin.phone')); ?></th>
                                                                <th><?php echo e(__('admin.bank_account')); ?></th>
                                                                <th><?php echo e(__('admin.amount')); ?></th>
                                                                <th><?php echo e(__('admin.created_at')); ?></th>
                                                                <th><?php echo e(__('admin.status')); ?></th>
                                                                <th><?php echo e(__('admin.image')); ?></th>
                                                    
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php $__currentLoopData = $user->provider->withdrawRequests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $withdrawRequest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <tr>
                                                                    <td><?php echo e($index + 1); ?></td>
                                                                    <td>
                                                                        <span class="badge badge-info"><?php echo e($withdrawRequest->number); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <?php if($withdrawRequest->provider): ?>
                                                                            <div>
                                                                                <strong><?php echo e($withdrawRequest->provider->commercial_name); ?></strong><br>
                                                                                <small class="text-muted"><?php echo e($withdrawRequest->provider->user->phone ?? '-'); ?></small>
                                                                            </div>
                                                                        <?php else: ?>
                                                                            <span class="text-muted">-</span>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td><?php echo e($withdrawRequest->provider->user->phone ?? '-'); ?></td>
                                                                    <td>
                                                                        <?php if($withdrawRequest->provider && $withdrawRequest->provider->bankAccount): ?>
                                                                            <div>
                                                                                <strong><?php echo e($withdrawRequest->provider->bankAccount->bank_name); ?></strong><br>
                                                                                <small><?php echo e($withdrawRequest->provider->bankAccount->account_number); ?></small><br>
                                                                                <small><?php echo e($withdrawRequest->provider->bankAccount->iban); ?></small>
                                                                            </div>
                                                                        <?php else: ?>
                                                                            <span class="text-muted">-</span>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td><?php echo e(number_format($withdrawRequest->amount, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                                                                    <td>
                                                                        <div><?php echo e($withdrawRequest->created_at->format('d/m/Y')); ?></div>
                                                                        <small class="text-muted"><?php echo e($withdrawRequest->created_at->format('H:i')); ?></small>
                                                                    </td>
                                                                    <td>
                                                                        <?php
                                                                            $statusClass = [
                                                                                'pending' => 'badge-warning',
                                                                                'accepted' => 'badge-success',
                                                                                'rejected' => 'badge-danger',
                                                                            ][$withdrawRequest->status] ?? 'badge-secondary';
                                                                        ?>
                                                                        <span class="badge <?php echo e($statusClass); ?>"><?php echo e(__('admin.' . $withdrawRequest->status)); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <?php if($withdrawRequest->status === 'accepted'): ?>
                                                                            <?php
                                                                                $imageUrl = $withdrawRequest->getFirstMediaUrl('withdraw_requests');
                                                                            ?>
                                                                            <?php if($imageUrl): ?>
                                                                                <a href="<?php echo e($imageUrl); ?>" target="_blank" title="<?php echo e(__('admin.image')); ?>">
                                                                                    <i class="fa fa-image fa-lg"></i>
                                                                                </a>
                                                                            <?php endif; ?>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                </tr>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </tbody>
                                                        
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                    <?php else: ?>
                                        <div class="alert alert-info mt-2 text-center">
                                            <i class="feather icon-info"></i> <?php echo e(__('admin.no_data_found')); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script>
        $(document).ready(function(){
            // Handle provider approval
            $(document).on('click','.approve-provider',function(e){
                e.preventDefault();
                let providerId = $(this).data('id');

                Swal.fire({
                    title: '<?php echo e(__('admin.are_you_sure')); ?>',
                    text: '<?php echo e(__('admin.approve_provider_request')); ?>',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: '<?php echo e(__('admin.approve_request')); ?>',
                    cancelButtonText: '<?php echo e(__('admin.cancel')); ?>'
                }).then(function(result) {
                    if (result.value) {
                        $.ajax({
                            url: '<?php echo e(url('admin/providers')); ?>/' + providerId + '/approve',
                            method: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                Swal.fire({
                                    title: '<?php echo e(__('admin.success')); ?>',
                                    text: response.message,
                                    type: 'success',
                                    confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                                });
                                setTimeout(function(){
                                    window.location.reload()
                                }, 1000);
                            },
                            error: function(xhr, status, error) {
                                var errorMessage = xhr.responseJSON?.message || '<?php echo e(__('admin.error_occurred')); ?>';
                                Swal.fire({
                                    title: '<?php echo e(__('admin.error')); ?>',
                                    text: errorMessage,
                                    type: 'error',
                                    confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                                });
                            }
                        });
                    }
                });
            });

            // Handle provider rejection
            $(document).on('click','.reject-provider',function(e){
                e.preventDefault();
                let providerId = $(this).data('id');

                Swal.fire({
                    title: '<?php echo e(__('admin.rejection_reason')); ?>',
                    input: 'textarea',
                    inputPlaceholder: '<?php echo e(__('admin.enter_rejection_reason')); ?>',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: '<?php echo e(__('admin.reject_request')); ?>',
                    cancelButtonText: '<?php echo e(__('admin.cancel')); ?>',
                    inputValidator: function(value) {
                        if (!value) {
                            return '<?php echo e(__('admin.reason_required')); ?>'
                        }
                    }
                }).then(function(result) {
                    if (result.value) {
                        var rejectionReason = result.value;
                        $.ajax({
                            url: '<?php echo e(url('admin/providers')); ?>/' + providerId + '/reject',
                            method: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>',
                                rejection_reason: rejectionReason
                            },
                            success: function(response) {
                                Swal.fire({
                                    title: '<?php echo e(__('admin.success')); ?>',
                                    text: response.message,
                                    type: 'success',
                                    confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                                });
                                setTimeout(function(){
                                    window.location.reload()
                                }, 1000);
                            },
                            error: function(xhr, status, error) {
                                var errorMessage = xhr.responseJSON?.message || '<?php echo e(__('admin.error_occurred')); ?>';
                                Swal.fire({
                                    title: '<?php echo e(__('admin.error')); ?>',
                                    text: errorMessage,
                                    type: 'error',
                                    confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/providers/show.blade.php ENDPATH**/ ?>