<?php

namespace App\Http\Requests\Admin\Provider;

use App\Rules\ProviderPhoneUnique;
use App\Rules\EmailUniqueWhenNotNull;
use Illuminate\Foundation\Http\FormRequest;

class Update extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $userId = $this->route('id');
        
        // Check if user has existing residence image
        $user = \App\Models\User::with('provider')->find($userId);
        $hasExistingResidenceImage = $user && $user->provider && $user->provider->getFirstMedia('residence_image');
        
        return [
            // User basic fields
            'name'                      => 'required|max:50',
            'country_code'              => 'required|numeric|digits_between:2,5',
            'phone'                     => [
                'required',
                new ProviderPhoneUnique($userId)
            ],
            'email'                     => [
                'sometimes',
                'nullable',
                'email',
                'max:50',
                EmailUniqueWhenNotNull::forUsers($userId)
            ],
            'password'                  => 'nullable|confirmed|min:6|max:100',
            'city_id'                   => 'required|exists:cities,id',
            'region_id'                   => 'required|exists:regions,id',

            'gender'                    => 'required|in:male,female',
             'image'                     => 'nullable|image|max:2048',

            // Provider specific fields
            'commercial_name'           => 'required|array',
            'commercial_name.ar'        => 'required|string|max:100',
            'commercial_name.en'        => 'nullable|string|max:100',
            'salon_type'                => 'required|string|in:salon,beauty_center,freelancer',
            'residence_type'            => 'required_if:nationality,other|nullable|string|in:individual,professional',
            'nationality'               => 'required|string|in:saudi,other',
            
            'commercial_register_no'    => 'required_unless:salon_type,freelancer|nullable|string|max:50',
            'sponsor_name'              => 'required_if:nationality,other|nullable|string|max:100',
            'sponsor_phone'             => 'required_if:nationality,other|nullable|string|max:20',
            'institution_name'          => 'required_unless:salon_type,freelancer|nullable|string|max:100',
            'in_home'                   => 'nullable|boolean',
            'in_salon'                  => 'nullable|boolean',
            'comission'                 => 'nullable|numeric|min:0|max:999999.99',

            // Document uploads
            'logo'                      => 'nullable|image|max:2048',
            'commercial_register_image' => 'nullable|image|max:2048',
            'residence_image'           => $hasExistingResidenceImage ? 'nullable|image|max:2048' : 'required_if:nationality,other|nullable|image|max:2048',

            // Freelancer specific fields
            'freelance_document_image'  => 'nullable|image|max:2048',
            'id_number'                 => 'nullable|string|size:10',
            'id_image'                  => 'nullable|image|max:2048',

            // Working hours validation (optional)
            'working_hours'                     => 'sometimes|nullable|array',
            'working_hours.*.day'               => 'sometimes|string|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
            'working_hours.*.start_time'        => 'required_if:working_hours.*.is_working,1|nullable',
            'working_hours.*.end_time'          => 'required_if:working_hours.*.is_working,1|nullable|after:working_hours.*.start_time',
            'working_hours.*.is_working'        => 'sometimes|boolean',
        ];

        // Add conditional validation for freelancer ID fields
        if ($this->salon_type === 'freelancer' && $this->nationality === 'saudi') {
            $rules['id_number'] = 'required|string|size:10';
            $rules['id_image'] = 'required|image|max:2048';
        }

        // Remove commercial register validation for freelancers
        if ($this->salon_type === 'freelancer') {
            $rules['commercial_register_no'] = 'nullable|string|min:10|max:50';
            $rules['commercial_register_image'] = 'nullable|image|max:2048';
        }

        return $rules;
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'                      => __('admin.name') . ' ' . __('admin.this_field_is_required'),
            'name.max'                          => __('admin.name') . ' ' . __('admin.max_length_50'),
            'country_code.required'             => __('admin.country_code') . ' ' . __('admin.this_field_is_required'),
            'country_code.numeric'              => __('admin.country_code') . ' ' . __('admin.must_be_numeric'),
            'country_code.digits_between'       => __('admin.country_code') . ' ' . __('admin.country_code_digits_between_2_5'),
            'phone.required'                    => __('admin.phone_number') . ' ' . __('admin.this_field_is_required'),
            'phone.min'                         => __('admin.phone_number') . ' ' . __('admin.phone_min_8_digits'),
            'phone.unique'                      => __('admin.phone_number') . ' ' . __('admin.phone_already_exists'),
            'email.email'                       => __('admin.email') . ' ' . __('admin.email_formula_is_incorrect'),
            'email.max'                         => __('admin.email') . ' ' . __('admin.max_length_191'),
            'email.unique'                      => __('admin.email') . ' ' . __('admin.email_already_exists'),
            'password.min'                      => __('admin.password') . ' ' . __('admin.password_min_6_characters'),
            'city_id.required'                  => __('admin.City') . ' ' . __('admin.this_field_is_required'),
            'city_id.exists'                    => __('admin.City') . ' ' . __('admin.city_not_found'),
            'status.required'                   => __('admin.status') . ' ' . __('admin.this_field_is_required'),
            'status.in'                         => __('admin.status') . ' ' . __('admin.invalid_status'),
            'image.image'                       => __('admin.profile_image') . ' ' . __('admin.must_be_image'),
            'image.max'                         => __('admin.profile_image') . ' ' . __('admin.image_max_size_2mb'),
            'commercial_name.max'               => __('admin.commercial_name') . ' ' . __('admin.max_length_191'),
            'commercial_register_no.max'        => __('admin.commercial_register_no') . ' ' . __('admin.max_length_50'),
            'institution_name.max'              => __('admin.institution_name') . ' ' . __('admin.max_length_100'),
            'sponsor_name.max'                  => __('admin.sponsor_name') . ' ' . __('admin.max_length_100'),
            'sponsor_phone.max'                 => __('admin.sponsor_phone') . ' ' . __('admin.max_length_20'),
            'nationality.max'                   => __('admin.nationality') . ' ' . __('admin.max_length_100'),
            'residence_type.in'                 => __('admin.residence_type') . ' ' . __('admin.invalid_residence_type'),
            'salon_type.in'                     => __('admin.salon_type') . ' ' . __('admin.invalid_salon_type'),
            'description.max'                   => __('admin.max_length_1000'),
            'comission.numeric'                 => __('admin.must_be_numeric'),
            'comission.min'                     => __('admin.must_be_positive'),
            'comission.max'                     => __('admin.max_amount_999999'),
            'mobile_service_fee.numeric'        => __('admin.must_be_numeric'),
            'mobile_service_fee.min'            => __('admin.must_be_positive'),
            'mobile_service_fee.max'            => __('admin.max_amount_999999'),
            'logo.image'                        => __('admin.must_be_image'),
            'logo.max'                          => __('admin.image_max_size_2mb'),
            'commercial_register_image.image'   => __('admin.must_be_image'),
            'commercial_register_image.max'     => __('admin.image_max_size_2mb'),
            'residence_image.image'             => __('admin.residence_image') . ' ' . __('admin.must_be_image'),
            'residence_image.max'               => __('admin.residence_image') . ' ' . __('admin.image_max_size_2mb'),

            // Freelancer specific validation messages
            'freelance_document_image.required_if'   => __('admin.freelance_document_image') . ' ' . __('admin.this_field_is_required'),
            'freelance_document_image.image'         => __('admin.freelance_document_image') . ' ' . __('admin.must_be_image'),
            'freelance_document_image.max'           => __('admin.freelance_document_image') . ' ' . __('admin.image_max_size_2mb'),
            'id_number.required'                     => __('admin.id_number') . ' ' . __('admin.this_field_is_required'),
            'id_number.string'                       => __('admin.id_number') . ' ' . __('admin.must_be_text'),
            'id_number.size'                         => __('admin.id_number') . ' ' . __('admin.id_number_must_be_10_digits'),
            'id_image.required'                      => __('admin.id_image') . ' ' . __('admin.this_field_is_required'),
            'id_image.image'                         => __('admin.id_image') . ' ' . __('admin.must_be_image'),
            'id_image.max'                           => __('admin.id_image') . ' ' . __('admin.image_max_size_2mb'),

            // Working hours validation messages
            'working_hours.array'                       => __('admin.working_hours_must_be_array'),
            'working_hours.*.day.in'                    => __('admin.invalid_day'),
            'working_hours.*.start_time.required_if'    => __('admin.start_time_is_required'),
            'working_hours.*.start_time.date_format'    => __('admin.invalid_time_format'),
            'working_hours.*.end_time.required_if'      => __('admin.end_time_is_required'),
            'working_hours.*.end_time.date_format'      => __('admin.invalid_time_format'),
            'working_hours.*.end_time.after'            => __('admin.end_time_must_be_after_start_time'),
        ];
    }



    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Convert checkbox values to boolean
        $this->merge([
            'in_home' => $this->has('in_home') ? true : false,
            'in_salon' => $this->has('in_salon') ? true : false,
            'is_mobile' => $this->has('is_mobile') ? true : false,
        ]);

        // Fix phone number format if needed
        if ($this->has('phone')) {
            $this->merge([
                'phone' => ltrim($this->phone, '0'),
            ]);
        }

        // Clean up working hours data - only keep enabled days
        if ($this->has('working_hours')) {
            $workingHours = [];
            foreach ($this->working_hours as $day => $workingHour) {
                // Only include days that are marked as working
                if (isset($workingHour['is_working']) && $workingHour['is_working']) {
                    $workingHours[$day] = [
                        'day' => $workingHour['day'],
                        'start_time' => $workingHour['start_time'],
                        'end_time' => $workingHour['end_time'],
                        'is_working' => true
                    ];
                }
            }
            $this->merge(['working_hours' => $workingHours]);
        }

        // Remove password if empty (to keep current password)
        if (empty($this->password)) {
            $this->request->remove('password');
        }
    }
}
