

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/pickers/pickadate/pickadate.css')); ?>">
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="<?php echo e(route('admin.coupons.store')); ?>" class="store form-horizontal" novalidate>
                            <?php echo csrf_field(); ?>
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="coupon-name"><?php echo e(__('admin.coupon_name')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="coupon_name" class="form-control" placeholder="<?php echo e(__('admin.enter_coupon_name')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="coupon-number"><?php echo e(__('admin.coupon_number')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="coupon_num" class="form-control" placeholder="<?php echo e(__('admin.enter_coupon_number')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="provider-select"><?php echo e(__('admin.provider')); ?></label>
                                            <div class="controls">
                                                <select name="provider_id" class="select2 form-control" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    <option value=""><?php echo e(__('admin.select_provider')); ?></option>
                                                    <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($provider->id); ?>"><?php echo e($provider->commercial_name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.discount_type')); ?></label>
                                            <div class="controls">
                                                <select name="type" class="select2 form-control type" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                                    <option value><?php echo e(__('admin.select_the_discount_state')); ?></option>
                                                    <option value="ratio"><?php echo e(__('admin.Percentage')); ?></option>
                                                    <option value="number"><?php echo e(__('admin.fixed_number')); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.discount_value')); ?></label>
                                            <div class="controls">
                                                <input type="number"  name="discount" class="discount form-control" placeholder="<?php echo e(__('admin.type_the_value_of_the_discount')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.larger_value_for_discount')); ?></label>
                                            <div class="controls">
                                                <input readonly type="number" name="max_discount" class="max_discount form-control" placeholder="<?php echo e(__('admin.write_the_greatest_value_for_the_discount')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.start_date')); ?></label>
                                            <div class="controls">
                                                <input type="date" name="start_date" class="form-control" required
                                                    data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="expire-date"><?php echo e(__('admin.expiry_date')); ?></label>
                                            <div class="controls">
                                                <input type="date" name="expire_date" class="form-control">
                                                <small class="form-text text-muted"><?php echo e(__('admin.leave_empty_for_no_expiry')); ?></small>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.add')); ?></button>
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/pickers/pickadate/picker.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/pickers/pickadate/picker.date.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/pickers/dateTime/pick-a-datetime.js')); ?>"></script>

    <script>
        $(document).on('change','.select2', function () {
            if ($(this).val() == 'ratio') {
                $('.max_discount').prop('readonly', false);
            }else{
                $('.max_discount').prop('readonly', true);
            }
        });
    </script>
    <script>
        $(document).on('keyup','.discount', function () {
            if ($('.select2').val() == 'number') {
                $('.max_discount').val($(this).val());
            }else{
                if ($(this).val() > 100) {
                    $(this).val(null)
                    toastr.error('<?php echo e(__('admin.Percentage_must_not_bigger_than_100')); ?>')
                }
                $('.max_discount').val(null);
            }
        });

    </script>

    
        <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
        <?php echo $__env->make('admin.shared.submitAddForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/coupons/create.blade.php ENDPATH**/ ?>