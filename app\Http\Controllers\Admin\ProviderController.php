<?php

namespace App\Http\Controllers\Admin;

use App\Jobs\Notify;
use App\Models\City;
use App\Models\User;
use App\Jobs\SendSms;
use App\Models\Order;
use App\Mail\SendMail;
use App\Models\Region;
use App\Traits\Report;
use App\Models\Country;
use App\Models\Provider;
use App\Models\Complaint;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use App\Imports\ClientImport;
use App\Notifications\NotifyUser;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Services\TransactionService;
use App\Services\WorkingHoursService;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Requests\Admin\Provider\Store;
use App\Http\Requests\Admin\Provider\Update;
use Illuminate\Support\Facades\Notification;
use App\Http\Requests\Admin\Client\BalanceRequest;

class ProviderController extends Controller
{
    /**
     * Display all providers
     */
    public function index($id = null)
    {
        if (request()->ajax()) {
            $searchArray = request()->searchArray;
            
            // Add region_id to search array if it exists in the request
            if (request()->has('region_id')) {
                $searchArray['region_id'] = request()->region_id;
            }
            if (request()->has('city_id')) {
                $searchArray['city_id'] = request()->city_id;
            }

            $rows = User::with(['provider', 'city', 'region'])
                ->search($searchArray)
                ->where('type', 'provider')
                ->whereHas('provider' , function ($q)  {
                    $q->whereNotIn('status', ['in_review' , 'rejected'] );
                })
                ->paginate(30);
                
            $html = view('admin.providers.table', compact('rows'))->render();
            return response()->json(['html' => $html]);
        }
        // For non-AJAX, also only show users with provider data
        $rows = User::with(['provider', 'city', 'region'])
            ->where('type', 'provider')
            ->whereHas('provider')
            ->paginate(30);
        return view('admin.providers.index', ['rows' => $rows, 'region_id' => request('region_id'), 'city_id' => request('city_id')]);
    }

    /**
     * Display pending provider requests
     */
    public function pendingRequests($id = null)
    {
        if (request()->ajax()) {
            $rows = User::with(['provider', 'city', 'region'])
                ->search(request()->searchArray)
                ->where('type', 'provider')
                ->whereHas('provider', function($query) {
                    $query->whereIn('status', ['in_review' , 'rejected']);
                })
                ->paginate(30);
            $html = view('admin.providers.pending_table', compact('rows'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.providers.pending_requests');
    }

    /**
     * Show provider details
     */
    public function show($id)
    {
        $user = User::with(['provider', 'city', 'region', 'orders'])->findOrFail($id);

        if ($user->type !== 'provider') {
            abort(404, 'Provider not found');
        }

        return view('admin.providers.show', compact('user'));
    }

    /**
     * Show create provider form
     */
    public function create()
    {
        $regions = Region::where('is_active' , 1)->whereHas('cities')->get();
        $cities  = []; // Initially empty, will be populated via AJAX

        $countries = Country::where('is_active' , 1)->get();
        $settings = SiteSetting::pluck('value', 'key')->toArray();
            $salonCommission = $settings['salon_comission'] ?? 0;

        // Get settings for default country

        return view('admin.providers.create', get_defined_vars());
    }

    /**
     * Store new provider
     */
    public function store(Store $request)
    {
        DB::beginTransaction();
        try {
            // Prepare user data
            $userData = $request->only([
                'name', 'email', 'phone', 'country_code', 'city_id', 'gender', 'region_id'
            ]);
            $userData['type'] = 'provider';
            $userData['is_active'] = 1;
            $userData['password'] = bcrypt($request->password);

            $user = User::create($userData);

            // Handle profile image
            if ($request->hasFile('image')) {
                $user->addMedia($request->file('image'))
                    ->toMediaCollection('profile');
            }

            // Create provider record
            $providerData = $request->only([
                'commercial_name', 'commercial_register_no', 'institution_name',
                'sponsor_name', 'sponsor_phone', 'nationality',
                'residence_type', 'in_home', 'in_salon', 'comission', 'salon_type',
                'id_number'
            ]);

            $providerData['user_id'] = $user->id;
            $providerData['status'] = 'accepted';

            $provider = Provider::create($providerData);

            // Handle provider images
            if ($request->hasFile('logo')) {
                $provider->addMedia($request->file('logo'))
                    ->toMediaCollection('logo');
            }

            if ($request->hasFile('commercial_register_image')) {
                $provider->addMedia($request->file('commercial_register_image'))
                    ->toMediaCollection('commercial_register_image');
            }

            if ($request->hasFile('residence_image')) {
                $provider->addMedia($request->file('residence_image'))
                    ->toMediaCollection('residence_image');
            }

            // Handle freelancer specific images
            if ($request->hasFile('freelance_document_image')) {
                $provider->addMedia($request->file('freelance_document_image'))
                    ->toMediaCollection('freelance_document_image');
            }

            if ($request->hasFile('id_image')) {
                $provider->addMedia($request->file('id_image'))
                    ->toMediaCollection('id_image');
            }

            if ($request->hasFile('salon_images')) {
                $provider->clearMediaCollection('salon_images');
                foreach ($request->file('salon_images') as $salonImage) {
                    if ($salonImage instanceof UploadedFile) {
                        $provider->addMedia($salonImage)->toMediaCollection('salon_images');
                    }
                }
            }

            // Handle working hours
            \Log::info('Working hours data received:', ['working_hours' => $request->working_hours]);
            if ($request->has('working_hours') && !empty($request->working_hours)) {
                try {
                    $workingHoursService = new WorkingHoursService();
                    $result = $workingHoursService->storeWorkingHours($provider->id, ['working_hours' => $request->working_hours]);
                    \Log::info('Working hours created successfully:', ['result' => $result->toArray()]);
                } catch (\Exception $e) {
                    \Log::error('Working hours creation failed: ' . $e->getMessage());
                    \Log::error('Stack trace: ' . $e->getTraceAsString());
                    // Don't fail the entire provider creation if working hours fail
                }
            } else {
                \Log::info('No working hours data provided or empty array');
            }

            DB::commit();
            Report::addToLog('إضافة مزود خدمة جديد');
            return response()->json(['url' => route('admin.providers.index')]);

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Provider creation failed: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create provider: ' . $e->getMessage()], 500);
        }
    }

    public function block(Request $request) {
        $user = Provider::findOrFail($request->id);
        if($user->status =='blocked'){
            $user->update(['status' => 'pending']);

    }else{
        $user->update(['status' => 'blocked']);

    }

        Notification::send($user, new BlockUser($request->all()));
        return response()->json(['message' => $user->refresh()->status == 'blocked' ? __('admin.client_blocked') :  __('admin.client_unblocked')]);
    }


    /**
     * Show edit provider form
     */
    public function edit($id)
    {
        $user = User::with('provider')->findOrFail($id);

        if ($user->type !== 'provider') {
            abort(404, 'Provider not found');
        }

        $regions = Region::where('is_active' , 1)->whereHas('cities')->get();

        // Get cities for the user's region if they have one
        $cities = [];
        if ($user->region_id) {
            $cities = City::where('is_active' , 1)->where('region_id', $user->region_id)->get();
        }

        $supported_countries = SiteSetting::where('key', 'countries')->first()->value ?? '';
        $supported_countries = json_decode($supported_countries);
        $settings = SiteSetting::pluck('value', 'key')->toArray();
        $salonCommission = $settings['salon_comission'] ?? 0;

        // Get settings for default country
        $settings = [];
        $countries = Country::where('is_active' , 1)->get();

        return view('admin.providers.edit', get_defined_vars());
    }

    /**
     * Update provider
     */
    public function update(Update $request, $id)
    {
        DB::beginTransaction();
        try {
            $user = User::with('provider')->findOrFail($id);

            if ($user->type !== 'provider') {
                abort(404, 'Provider not found');
            }

                $user->update($request->validated());

            // Handle profile image
            if ($request->hasFile('image')) {
                $user->clearMediaCollection('profile');
                $user->addMedia($request->file('image'))
                    ->toMediaCollection('profile');
            }

            // Update provider data if exists
            if ($user->provider && $request->has('commercial_name')) {
                $providerData = $request->only([
                    'commercial_name', 'commercial_register_no', 'institution_name',
                    'sponsor_name', 'sponsor_phone',
                    'description', 'nationality', 'lat', 'lng', 'map_desc',
                    'residence_type', 'in_home', 'in_salon', 'comission', 'salon_type',
                    'id_number'
                ]);

                $user->provider->update($providerData);

                // Handle provider images
                if ($request->hasFile('logo')) {
                    $user->provider->clearMediaCollection('logo');
                    $user->provider->addMedia($request->file('logo'))
                        ->toMediaCollection('logo');
                }

                if ($request->hasFile('commercial_register_image')) {
                    $user->provider->clearMediaCollection('commercial_register_image');
                    $user->provider->addMedia($request->file('commercial_register_image'))
                        ->toMediaCollection('commercial_register_image');
                }

                if ($request->hasFile('residence_image')) {
                    $user->provider->clearMediaCollection('residence_image');
                    $user->provider->addMedia($request->file('residence_image'))
                        ->toMediaCollection('residence_image');
                }

                // Handle freelancer specific images
                if ($request->hasFile('freelance_document_image')) {
                    $user->provider->clearMediaCollection('freelance_document_image');
                    $user->provider->addMedia($request->file('freelance_document_image'))
                        ->toMediaCollection('freelance_document_image');
                }

                if ($request->hasFile('id_image')) {
                    $user->provider->clearMediaCollection('id_image');
                    $user->provider->addMedia($request->file('id_image'))
                        ->toMediaCollection('id_image');
                }

                if ($request->hasFile('salon_images')) {
                    $user->provider->clearMediaCollection('salon_images');
                    foreach ($request->file('salon_images') as $salonImage) {
                        if ($salonImage instanceof UploadedFile) {
                            $user->provider->addMedia($salonImage)->toMediaCollection('salon_images');
                        }
                    }
                }

                // Handle working hours
                \Log::info('Working hours data received for update:', ['working_hours' => $request->working_hours]);
                if ($request->has('working_hours') && !empty($request->working_hours)) {
                    try {
                        $workingHoursService = new WorkingHoursService();
                        $result = $workingHoursService->storeWorkingHours($user->provider->id, ['working_hours' => $request->working_hours]);
                        \Log::info('Working hours updated successfully:', ['result' => $result->toArray()]);
                    } catch (\Exception $e) {
                        \Log::error('Working hours update failed: ' . $e->getMessage());
                        \Log::error('Stack trace: ' . $e->getTraceAsString());
                        // Don't fail the entire provider update if working hours fail
                    }
                } else {
                    \Log::info('No working hours data provided for update or empty array');
                }
            }

            DB::commit();
            Report::addToLog('تعديل مزود خدمة');
            return response()->json(['url' => route('admin.providers.index')]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to update provider'], 500);
        }
    }

    /**
     * Approve provider request
     */
    public function approveRequest($id)
    {
        \Log::info('Approve request received for user ID: ' . $id);

        $user = User::with('provider')->findOrFail($id);

        if ($user->type !== 'provider' || !$user->provider) {
            \Log::error('Provider not found for user ID: ' . $id);
            return response()->json(['error' => 'Provider not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Update provider status to accepted
            $user->provider->update(['status' => 'accepted']);

            // Update user status and activate

            // Send notification to provider
            $user->notify(new NotifyUser([
                'title' => [
                    'ar' => 'تم قبول طلب التسجيل كمزود خدمة',
                    'en' => 'Provider Registration Request Approved'
                ],
                'body' => [
                    'ar' => 'تم قبول طلبك للتسجيل كمزود خدمة بنجاح. يمكنك الآن تسجيل الدخول وإدارة خدماتك.',
                    'en' => 'Your provider registration request has been approved successfully. You can now login and manage your services.'
                ],
                'type' => 'admin_notify'
            ]));

            DB::commit();
            Report::addToLog('قبول طلب مزود خدمة');
            return response()->json(['success' => true, 'message' => __('admin.provider_approved_successfully')]);

        } catch (\Exception $e) {
            dd($e);
            DB::rollback();
            return response()->json(['error' => 'Failed to approve provider'], 500);
        }
    }

    /**
     * Reject provider request
     */
    public function rejectRequest(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        $user = User::with('provider')->findOrFail($id);

        if ($user->type !== 'provider' || !$user->provider) {
            return response()->json(['error' => 'Provider not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Update provider status to rejected
            $user->provider->update([
                'status' => 'rejected',
                'rejection_reason' => $request->rejection_reason
            ]);

            // Update user status and deactivate
            $user->update([
                'status' => 'rejected',
                'is_active' => 0
            ]);

            // Send notification to provider
            $user->notify(new NotifyUser([
                'title' => [
                    'ar' => 'تم رفض طلب التسجيل',
                    'en' => 'Provider Registration Request Rejected'
                ],
                'body' => [
                    'ar' => 'تم رفض طلبك للتسجيل كمزود خدمة. السبب: ' . $request->rejection_reason . '. لا يمكن إعادة التسجيل بنفس رقم الهاتف.',
                    'en' => 'Your provider registration request has been rejected. Reason: ' . $request->rejection_reason . '. You cannot re-register with the same phone number.'
                ],
                'type' => 'admin_notify'
            ]));

            DB::commit();
            Report::addToLog('رفض طلب مزود خدمة - ' . $user->name . ' - السبب: ' . $request->rejection_reason);
            return response()->json(['success' => true, 'message' => __('admin.provider_rejected_successfully')]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['error' => 'Failed to reject provider'], 500);
        }
    }

    /**
     * Change provider status
     */
    public function changeStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:providers,id',
            'status' => 'required|in:in_review,pending,accepted,rejected,blocked,deleted'
        ]);

        $provider = Provider::with('user')->findOrFail($request->id);

        if (!$provider->user || $provider->user->type !== 'provider') {
            return response()->json(['error' => 'Provider not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Update provider status
            $provider->update(['status' => $request->status]);

            // Update user status based on provider status
            $userStatus = $this->mapProviderStatusToUserStatus($request->status);
            // $provider->user->update(['status' => $userStatus]);

            // // Handle special cases
            // if ($request->status === 'accepted') {
            //     $provider->user->update(['is_active' => 1]);
            // } elseif (in_array($request->status, ['blocked', 'rejected', 'deleted'])) {
            //     $provider->user->update(['is_active' => 0]);
            // }

            DB::commit();
            Report::addToLog('تغيير حالة مزود الخدمة - ' . $provider->user->name);

            return response()->json([
                'message' => __('admin.status_updated_successfully'),
                'status' => $request->status
            ]);

        } catch (\Exception $e) {
            dd($e);
            DB::rollback();
            return response()->json(['error' => 'Failed to update status'], 500);
        }
    }

    /**
     * Map provider status to user status
     */
    private function mapProviderStatusToUserStatus($providerStatus)
    {
        $statusMap = [
            'in_review' => 'pending',
            'pending' => 'pending',
            'accepted' => 'active',
            'blocked' => 'blocked',
            'rejected' => 'rejected',
            'deleted' => 'deleted'
        ];

        return $statusMap[$providerStatus] ?? 'pending';
    }

    /**
     * Delete provider
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);

        if ($user->type !== 'provider') {
            return response()->json(['error' => 'Provider not found'], 404);
        }

        $user->delete();
        Report::addToLog('حذف مزود خدمة');
        return response()->json('success');
    }

    /**
     * Delete multiple providers
     */
    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }

        if (User::whereIntegerInRaw('id', $ids)->where('type', 'provider')->get()->each->delete()) {
            Report::addToLog('حذف العديد من مزودي الخدمة');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }

    /**
     * Update provider balance
     */
    public function updateBalance(BalanceRequest $request)
    {
        $user = User::findOrFail($request->user_id);

        if ($user->type !== 'provider') {
            return response()->json(['error' => 'Provider not found'], 404);
        }

        $amount = convert2english($request->balance);

        DB::beginTransaction();
        try {
            if ($amount > 0) {
                (new TransactionService)->adminAddtoUserWallet($user, $amount);
            } elseif ($amount < 0) {
                (new TransactionService)->adminCutFromUserWallet($user, $amount);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
        }

        return redirect()->back()->with('success', __('admin.update_successfullay'));
    }

    /**
     * Send notification to provider
     */
    public function notify(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000'
        ]);

        $users = User::whereIn('id', $request->ids)->where('type', 'provider')->get();

        Notification::send($users, new NotifyUser([
            'title' => [
                'ar' => $request->title,
                'en' => $request->title
            ],
            'body' => [
                'ar' => $request->message,
                'en' => $request->message
            ],
            'type' => 'admin_notify'
        ]));

        Report::addToLog('إرسال إشعار لمزودي الخدمة');
        return response()->json('success');
    }
}
