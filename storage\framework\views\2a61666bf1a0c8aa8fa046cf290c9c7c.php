<div class="position-relative">
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.date')); ?></th>
                <th><?php echo e(__('admin.name_to_complain')); ?></th>
                <th><?php echo e(__('admin.phone_to_complain')); ?></th>
                <th><?php echo e(__('admin.email')); ?></th>

                <th><?php echo e(__('admin.type')); ?></th>
                <th><?php echo e(__('admin.title')); ?></th>
                <th><?php echo e(__('admin.message')); ?></th>
                <th><?php echo e(__('admin.type')); ?></th>
                <th><?php echo e(__('admin.mark_as_read')); ?></th>

            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $complaints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $complaint): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_complaint">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="<?php echo e($complaint->id); ?>">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e(\Carbon\Carbon::parse($complaint->created_at)->format('d/m/Y H:i:s')); ?></td>
                    <td><?php echo e($complaint->user->name); ?></td>
                    <td><?php echo e($complaint->user->phone); ?></td>
                    <td><?php echo e($complaint->user->email); ?></td>
                    <td><?php echo e(__('admin.'.$complaint->user->type)); ?></td>
                    <td><?php echo e($complaint->title); ?></td>
                    <td><?php echo e($complaint->body); ?></td>
                    <td><?php echo e(__('admin.' .$complaint->type)); ?></td>


                    <td class="product-action">
                        <?php echo toggleBooleanView($complaint , route('admin.model.active' , ['model' =>'ContactUs' , 'id' => $complaint->id , 'action' => 'is_read'])); ?>


                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($complaints->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($complaints->count() > 0 && $complaints instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($complaints->links()); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/complaints/table.blade.php ENDPATH**/ ?>