@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <style>
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #dc3545;
        }
        .form-control.is-invalid {
            border-color: #dc3545;
            padding-right: calc(1.5em + 0.75rem);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M6.2 7.4l1.4-1.4'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
    </style>
@endsection
{{-- extra css files --}}

@section('content')
<form method="POST" action="{{route('admin.images.store')}}" class="store form-horizontal" enctype="multipart/form-data">
    @csrf
    <section id="multiple-column-form">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">{{__('admin.add')}}</h4>
                    </div>
                    <div class="card-content">
                        <div class="card-body">
                            <div class="form-body"> 
                                <div class="row">
                                    {{-- Name Fields --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="name_ar">{{__('admin.name')}} ({{__('admin.ar')}})</label>
                                            <input type="text" id="name_ar" class="form-control @error('name.ar') is-invalid @enderror" name="name[ar]"
                                                placeholder="{{__('admin.write') . __('admin.name')}} {{__('admin.ar')}}"
                                                value="{{ old('name.ar') }}" required>
                                            @error('name.ar')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="name_en">{{__('admin.name')}} ({{__('admin.en')}})</label>
                                            <input type="text" id="name_en" class="form-control @error('name.en') is-invalid @enderror" name="name[en]"
                                                placeholder="{{__('admin.write') . __('admin.name')}} {{__('admin.en')}}"
                                                value="{{ old('name.en') }}" required>
                                            @error('name.en')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    {{-- Link Field --}}
                                    <div class="col-md-12 col-12">
                                        <div class="form-group">
                                            <label for="link">{{__('admin.link')}}</label>
                                            <input type="text" id="link" class="form-control @error('link') is-invalid @enderror" name="link"
                                                placeholder="{{__('admin.write') . __('admin.link')}}" value="{{ old('link') }}">
                                            @error('link')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    {{-- Image (AR) --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="image_ar">{{__('admin.image')}} ({{__('admin.ar')}})</label>
                                            <input type="file" id="image_ar" class="form-control @error('image_ar') is-invalid @enderror" name="image_ar" accept="image/*" required>
                                            @error('image_ar')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    {{-- Image (EN) --}}
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="image_en">{{__('admin.image')}} ({{__('admin.en')}})</label>
                                            <input type="file" id="image_en" class="form-control @error('image_en') is-invalid @enderror" name="image_en" accept="image/*" required>
                                            @error('image_en')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    {{-- Status --}}
                                    <div class="col-md-12 col-12">
                                        <div class="form-group">
                                            <label for="is_active">{{__('admin.status')}}</label>
                                            <select name="is_active" class="form-control @error('is_active') is-invalid @enderror">
                                                <option value="1" {{ old('is_active', '1') == '1' ? 'selected' : '' }}>{{__('admin.active')}}</option>
                                                <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>{{__('admin.inactive')}}</option>
                                            </select>
                                            @error('is_active')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{__('admin.add')}}</button>
                                        <a href="{{ url()->previous() }}" type="reset" class="btn btn-outline-warning mr-1 mb-1">{{__('admin.back')}}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>

@endsection
@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>

    {{-- show selected image script --}}
        @include('admin.shared.addImage')
    {{-- show selected image script --}}

    <script>
        $(document).ready(function() {
            // Frontend validation function
            function validateForm() {
                let isValid = true;

                // Clear previous errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').hide();

                // Validate Arabic name
                const nameAr = $('#name_ar').val().trim();
                if (!nameAr) {
                    $('#name_ar').addClass('is-invalid');
                    $('#name_ar').siblings('.invalid-feedback').text('الاسم باللغة العربية مطلوب').show();
                    isValid = false;
                } else if (nameAr.length < 2) {
                    $('#name_ar').addClass('is-invalid');
                    $('#name_ar').siblings('.invalid-feedback').text('الاسم باللغة العربية يجب أن يكون حرفين على الأقل').show();
                    isValid = false;
                }

                // Validate English name
                const nameEn = $('#name_en').val().trim();
                if (!nameEn) {
                    $('#name_en').addClass('is-invalid');
                    $('#name_en').siblings('.invalid-feedback').text('الاسم باللغة الإنجليزية مطلوب').show();
                    isValid = false;
                } else if (nameEn.length < 2) {
                    $('#name_en').addClass('is-invalid');
                    $('#name_en').siblings('.invalid-feedback').text('الاسم باللغة الإنجليزية يجب أن يكون حرفين على الأقل').show();
                    isValid = false;
                }

                // Validate Arabic image
                const imageArFiles = $('#image_ar')[0].files;
                if (imageArFiles.length === 0) {
                    $('#image_ar').addClass('is-invalid');
                    $('#image_ar').siblings('.invalid-feedback').text('الصورة باللغة العربية مطلوبة').show();
                    isValid = false;
                } else {
                    const file = imageArFiles[0];
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        $('#image_ar').addClass('is-invalid');
                        $('#image_ar').siblings('.invalid-feedback').text('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)').show();
                        isValid = false;
                    } else if (file.size > 5 * 1024 * 1024) { // 5MB
                        $('#image_ar').addClass('is-invalid');
                        $('#image_ar').siblings('.invalid-feedback').text('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت').show();
                        isValid = false;
                    }
                }

                // Validate English image
                const imageEnFiles = $('#image_en')[0].files;
                if (imageEnFiles.length === 0) {
                    $('#image_en').addClass('is-invalid');
                    $('#image_en').siblings('.invalid-feedback').text('الصورة باللغة الإنجليزية مطلوبة').show();
                    isValid = false;
                } else {
                    const file = imageEnFiles[0];
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        $('#image_en').addClass('is-invalid');
                        $('#image_en').siblings('.invalid-feedback').text('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)').show();
                        isValid = false;
                    } else if (file.size > 5 * 1024 * 1024) { // 5MB
                        $('#image_en').addClass('is-invalid');
                        $('#image_en').siblings('.invalid-feedback').text('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت').show();
                        isValid = false;
                    }
                }

                return isValid;
            }

            // Form submission with frontend and backend validation
            $('.store').on('submit', function(e) {
                e.preventDefault();

                // Run frontend validation first
                if (!validateForm()) {
                    return false;
                }

                var form = this;
                var formData = new FormData(form);

                // Show loading state
                $(".submit_button").html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحفظ...').attr('disabled', true);

                // Clear previous errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').hide();

                $.ajax({
                    url: $(form).attr('action'),
                    method: $(form).attr('method'),
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.url) {
                            Swal.fire({
                                position: 'top-start',
                                type: 'success',
                                title: 'تم إضافة الصورة بنجاح',
                                showConfirmButton: false,
                                timer: 1500,
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            }).then(() => {
                                window.location.href = response.url;
                            });
                        }
                    },
                    error: function(xhr) {
                        // Reset button state
                        $(".submit_button").html('{{__('admin.add')}}').attr('disabled', false);

                        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = xhr.responseJSON.errors;
                            Object.keys(errors).forEach(function(key) {
                                const errorMessage = errors[key][0];
                                const inputName = key.replace(/\./g, '_');
                                const inputElement = $(`[name="${key}"], [name="${key.replace(/\./g, '[')}"]`);

                                if (inputElement.length) {
                                    inputElement.addClass('is-invalid');
                                    inputElement.siblings('.invalid-feedback').text(errorMessage).show();
                                }
                            });
                        } else {
                            Swal.fire({
                                position: 'top-start',
                                type: 'error',
                                title: 'خطأ',
                                text: xhr.responseJSON?.message || 'حدث خطأ غير متوقع',
                                showConfirmButton: true,
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            });
                        }
                    }
                });
            });

            // Real-time validation and error clearing
            $('#name_ar').on('input', function() {
                const value = $(this).val().trim();
                if (value) {
                    $(this).removeClass('is-invalid');
                    $(this).siblings('.invalid-feedback').hide();
                } else {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('الاسم باللغة العربية مطلوب').show();
                }
            });

            $('#name_en').on('input', function() {
                const value = $(this).val().trim();
                if (value) {
                    $(this).removeClass('is-invalid');
                    $(this).siblings('.invalid-feedback').hide();
                } else {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('الاسم باللغة الإنجليزية مطلوب').show();
                }
            });

            $('#image_ar').on('change', function() {
                const files = this.files;
                if (files.length === 0) {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('الصورة باللغة العربية مطلوبة').show();
                } else {
                    const file = files[0];
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        $(this).addClass('is-invalid');
                        $(this).siblings('.invalid-feedback').text('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)').show();
                    } else if (file.size > 5 * 1024 * 1024) { // 5MB
                        $(this).addClass('is-invalid');
                        $(this).siblings('.invalid-feedback').text('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت').show();
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).siblings('.invalid-feedback').hide();
                    }
                }
            });

            $('#image_en').on('change', function() {
                const files = this.files;
                if (files.length === 0) {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('الصورة باللغة الإنجليزية مطلوبة').show();
                } else {
                    const file = files[0];
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        $(this).addClass('is-invalid');
                        $(this).siblings('.invalid-feedback').text('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)').show();
                    } else if (file.size > 5 * 1024 * 1024) { // 5MB
                        $(this).addClass('is-invalid');
                        $(this).siblings('.invalid-feedback').text('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت').show();
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).siblings('.invalid-feedback').hide();
                    }
                }
            });

            // Clear error for other fields
            $('#link, select[name="is_active"]').on('input change', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.invalid-feedback').hide();
            });
        });
    </script>

@endsection