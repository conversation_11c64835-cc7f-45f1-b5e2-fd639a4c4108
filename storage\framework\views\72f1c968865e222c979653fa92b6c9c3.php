<script>
    $(document).on('click' , '.delete-row', function (e) {
        e.preventDefault()
        Swal.fire({
            title: "<?php echo e(__('admin.are_you_want_to_continue')); ?>",
            text: "<?php echo e(__('admin.are_you_sure_you_want_to_complete_deletion')); ?>",
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '<?php echo e(__('admin.confirm')); ?>',
            confirmButtonClass: 'btn btn-primary',
            cancelButtonText: '<?php echo e(__('admin.cancel')); ?>',
            cancelButtonClass: 'btn btn-danger ml-1',
            buttonsStyling: false,
            }).then( (result) => {
            if (result.value) {
                $.ajax({
                    type: "delete",
                    url: $(this).data('url'),
                    data: {},
                    dataType: "json",
                    success:  (response) => {
                        Swal.fire(
                        {
                            position: 'top-start',
                            type: 'success',
                            title: '<?php echo e(__('admin.the_selected_has_been_successfully_deleted')); ?>',
                            showConfirmButton: false,
                            timer: 1500,
                            confirmButtonClass: 'btn btn-primary',
                            buttonsStyling: false,
                        })
                        getData({'searchArray' : searchArray()} )
                        // toastr.error()
                        // $('.data-list-view').DataTable().row($(this).closest('td').parent('tr')).remove().draw();
                    },
                    error: function(xhr) {
                        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.error) {
                            Swal.fire({
                                position: 'top-start',
                                type: 'error',
                                title: 'خطأ في الحذف',
                                text: xhr.responseJSON.error,
                                showConfirmButton: true,
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            });
                        } else {
                            Swal.fire({
                                position: 'top-start',
                                type: 'error',
                                title: 'خطأ',
                                text: 'حدث خطأ أثناء الحذف',
                                showConfirmButton: true,
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            });
                        }
                    }
                });
            }
        })
    });
</script><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/shared/deleteOne.blade.php ENDPATH**/ ?>