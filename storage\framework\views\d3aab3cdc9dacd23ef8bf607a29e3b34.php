<div class="position-relative">
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.created_at')); ?></th>
                <th><?php echo e(__('admin.name')); ?></th>

                <th><?php echo e(__('admin.image')); ?> (<?php echo e(__('admin.ar')); ?>)</th>
                <th><?php echo e(__('admin.image')); ?> (<?php echo e(__('admin.en')); ?>)</th>
                <th><?php echo e(__('admin.id_num')); ?></th>

                <th><?php echo e(__('admin.link')); ?></th>
                <th><?php echo e(__('admin.status')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_image">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="<?php echo e($image->id); ?>">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($image->created_at); ?></td>
                    <td><?php echo e($image->name); ?></td>
                    <td><img src="<?php echo e($image->image_ar); ?>" width="30px" height="30px" alt=""></td>
                    <td><img src="<?php echo e($image->image_en); ?>" width="30px" height="30px" alt=""></td>

                    <td><?php echo e($image->id); ?></td>

                    <td><a href="<?php echo e($image->link); ?>"><?php echo e($image->link); ?></a></td>
                    <td>
                        <?php echo toggleBooleanView($image , route('admin.model.active' , ['model' =>'Image' , 'id' => $image->id , 'action' => 'is_active'])); ?>

                    </td>
                    <td class="product-action">
                        <span class="text-primary"><a href="<?php echo e(route('admin.images.show', ['id' => $image->id])); ?>" class="btn btn-warning btn-sm"><i class="feather icon-eye"></i> <?php echo e(__('admin.show')); ?></a></span>
                        <span class="action-edit text-primary"><a href="<?php echo e(route('admin.images.edit', ['id' => $image->id])); ?>" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i><?php echo e(__('admin.edit')); ?></a></span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="<?php echo e(url('admin/images/' . $image->id)); ?>"><i class="feather icon-trash"></i><?php echo e(__('admin.delete')); ?></span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($images->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($images->count() > 0 && $images instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($images->links()); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/images/table.blade.php ENDPATH**/ ?>