<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>Invoice #<?php echo e($order->order_number); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .invoice-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .header h2 {
            color: #6c757d;
            margin: 5px 0;
            font-size: 18px;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .info-box {
            flex: 1;
            margin: 0 10px;
        }
        .info-box h3 {
            color: #007bff;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .info-item {
            margin-bottom: 8px;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .info-value {
            color: #6c757d;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
        }
        .items-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .totals-section {
            margin-top: 30px;
            border-top: 2px solid #dee2e6;
            padding-top: 20px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
        }
        .total-row.final {
            border-top: 2px solid #007bff;
            font-weight: bold;
            font-size: 18px;
            color: #007bff;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-processing { background-color: #fff3cd; color: #856404; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .admin-badge {
            background-color: #007bff;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <h1>فاتورة الطلب - إدارة النظام</h1>
            <h2>Invoice #<?php echo e($order->order_number); ?></h2>
            <p>تاريخ الطلب: <?php echo e($order->created_at->format('Y-m-d H:i')); ?></p>
            <span class="admin-badge">ADMIN VIEW</span>
        </div>

        <!-- Information Sections -->
        <div class="info-section">
            <!-- Customer Information -->
            <div class="info-box">
                <h3>معلومات العميل</h3>
                <div class="info-item">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value"><?php echo e($customer->name); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value"><?php echo e($customer->country_code); ?><?php echo e($customer->phone); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">البريد الإلكتروني:</span>
                    <span class="info-value"><?php echo e($customer->email ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع المستخدم:</span>
                    <span class="info-value"><?php echo e($customer->type === 'client' ? 'عميل' : 'مزود'); ?></span>
                </div>
            </div>

            <!-- Provider Information -->
            <div class="info-box">
                <h3>معلومات المزودين</h3>
                <?php if($providers && $providers->count() > 0): ?>
                    <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="info-item" style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 10px;">
                            <div style="font-weight: bold; color: #007bff; margin-bottom: 5px;">
                                <?php echo e($provider['commercial_name']); ?>

                            </div>
                            <div class="info-item">
                                <span class="info-label">رقم الطلب الفرعي:</span>
                                <span class="info-value"><?php echo e($provider['sub_order_number']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">رقم الهاتف:</span>
                                <span class="info-value"><?php echo e($provider['phone']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">البريد الإلكتروني:</span>
                                <span class="info-value"><?php echo e($provider['email']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">حالة الطلب:</span>
                                <span class="status-badge status-<?php echo e($provider['status']); ?>">
                                    <?php echo e(__('admin.' . $provider['status'])); ?>

                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">إجمالي الطلب:</span>
                                <span class="info-value"><?php echo e(number_format($provider['total'], 2)); ?> ريال</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">حصة المزود:</span>
                                <span class="info-value"><?php echo e(number_format($provider['provider_share'], 2)); ?> ريال</span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="info-item">
                        <span class="info-value">لا يوجد مزودين محددين</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Order Information -->
        <div class="info-section">
            <div class="info-box">
                <h3>معلومات الطلب</h3>
                <div class="info-item">
                    <span class="info-label">رقم الطلب:</span>
                    <span class="info-value"><?php echo e($order->order_number); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">حالة الطلب:</span>
                    <span class="status-badge status-<?php echo e($order->current_status); ?>">
                        <?php echo e(__('admin.' . $order->current_status)); ?>

                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">طريقة الدفع:</span>
                    <span class="info-value"><?php echo e($payment_method->name ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">حالة الدفع:</span>
                    <span class="info-value"><?php echo e(__('admin.' . $order->payment_status)); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع الحجز:</span>
                    <span class="info-value"><?php echo e(__('admin.' . $order->booking_type)); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع التوصيل:</span>
                    <span class="info-value"><?php echo e(__('admin.' . $order->delivery_type)); ?></span>
                </div>
            </div>

            <!-- Address Information -->
            <div class="info-box">
                <h3>معلومات العنوان</h3>
                <?php if($address): ?>
                    <div class="info-item">
                        <span class="info-label">العنوان:</span>
                        <span class="info-value"><?php echo e($address->details); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المدينة:</span>
                        <span class="info-value"><?php echo e($address->city->name ?? 'غير محدد'); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المنطقة:</span>
                        <span class="info-value"><?php echo e($address->region->name ?? 'غير محدد'); ?></span>
                    </div>
                <?php else: ?>
                    <div class="info-item">
                        <span class="info-value">لا يوجد عنوان محدد</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Order Items -->
        <h3>المنتجات والخدمات</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>النوع</th>
                    <th>المزود</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($item->name); ?></td>
                        <td>
                            <?php if($item->item_type === 'App\Models\Service'): ?>
                                خدمة
                            <?php else: ?>
                                منتج
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($item->item && $item->item->provider): ?>
                                <?php echo e($item->item->provider->commercial_name ?? $item->item->provider->user->name); ?>

                            <?php else: ?>
                                غير محدد
                            <?php endif; ?>
                        </td>
                        <td><?php echo e($item->quantity); ?></td>
                        <td><?php echo e(number_format($item->price, 2)); ?> ريال</td>
                        <td><?php echo e(number_format($item->total, 2)); ?> ريال</td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span>إجمالي المنتجات:</span>
                <span><?php echo e(number_format($totals['products_total'], 2)); ?> ريال</span>
            </div>
            <div class="total-row">
                <span>إجمالي الخدمات:</span>
                <span><?php echo e(number_format($totals['services_total'], 2)); ?> ريال</span>
            </div>
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span><?php echo e(number_format($totals['subtotal'], 2)); ?> ريال</span>
            </div>
            
            <?php if($totals['booking_fee'] > 0): ?>
                <div class="total-row">
                    <span>رسوم الحجز:</span>
                    <span><?php echo e(number_format($totals['booking_fee'], 2)); ?> ريال</span>
                </div>
            <?php endif; ?>
            
            <?php if($totals['home_service_fee'] > 0): ?>
                <div class="total-row">
                    <span>رسوم الخدمة المنزلية:</span>
                    <span><?php echo e(number_format($totals['home_service_fee'], 2)); ?> ريال</span>
                </div>
            <?php endif; ?>
            
            <?php if($totals['delivery_fee'] > 0): ?>
                <div class="total-row">
                    <span>رسوم التوصيل:</span>
                    <span><?php echo e(number_format($totals['delivery_fee'], 2)); ?> ريال</span>
                </div>
            <?php endif; ?>
            
            <?php if($totals['discount_amount'] > 0): ?>
                <div class="total-row">
                    <span>الخصم:</span>
                    <span>-<?php echo e(number_format($totals['discount_amount'], 2)); ?> ريال</span>
                </div>
            <?php endif; ?>
            
            <div class="total-row final">
                <span>الإجمالي النهائي:</span>
                <span><?php echo e(number_format($totals['total'], 2)); ?> ريال</span>
            </div>
        </div>

        <!-- Provider Shares and Commission -->
        <div class="totals-section">
            <h3>توزيع المبالغ</h3>
            <?php if($providers && $providers->count() > 0): ?>
                <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div style="border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; background-color: #f8f9fa;">
                        <h4 style="color: #007bff; margin-bottom: 10px;"><?php echo e($provider['commercial_name']); ?></h4>
                        <div class="total-row">
                            <span>إجمالي طلب المزود:</span>
                            <span><?php echo e(number_format($provider['total'], 2)); ?> ريال</span>
                        </div>
                        <div class="total-row">
                            <span>حصة المزود:</span>
                            <span style="color: #28a745; font-weight: bold;"><?php echo e(number_format($provider['provider_share'], 2)); ?> ريال</span>
                        </div>
                        <div class="total-row">
                            <span>عمولة المنصة:</span>
                            <span style="color: #dc3545; font-weight: bold;"><?php echo e(number_format($provider['total'] - $provider['provider_share'], 2)); ?> ريال</span>
                        </div>
                        <div class="total-row">
                            <span>نسبة العمولة:</span>
                            <span><?php echo e($provider['total'] > 0 ? number_format((($provider['total'] - $provider['provider_share']) / $provider['total']) * 100, 1) : 0); ?>%</span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                
                <!-- Total Platform Commission -->
                <?php
                    $totalProviderShares = $providers->sum('provider_share');
                    $totalPlatformCommission = $providers->sum('total') - $totalProviderShares;
                ?>
                <div style="border: 2px solid #007bff; border-radius: 8px; padding: 15px; margin-top: 20px; background-color: #e3f2fd;">
                    <h4 style="color: #007bff; margin-bottom: 10px; text-align: center;">إجمالي توزيع المبالغ</h4>
                    <div class="total-row">
                        <span>إجمالي مبالغ المزودين:</span>
                        <span><?php echo e(number_format($providers->sum('total'), 2)); ?> ريال</span>
                    </div>
                    <div class="total-row">
                        <span>إجمالي حصص المزودين:</span>
                        <span style="color: #28a745; font-weight: bold;"><?php echo e(number_format($totalProviderShares, 2)); ?> ريال</span>
                    </div>
                    <div class="total-row final">
                        <span>إجمالي عمولة المنصة:</span>
                        <span style="color: #dc3545; font-weight: bold;"><?php echo e(number_format($totalPlatformCommission, 2)); ?> ريال</span>
                    </div>
                </div>
            <?php else: ?>
                <div class="info-item">
                    <span class="info-value">لا يوجد مزودين محددين</span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذه الفاتورة في <?php echo e(now()->format('Y-m-d H:i:s')); ?></p>
            <p>فاتورة إدارية - نظام إدارة الطلبات</p>
        </div>
    </div>
</body>
</html> <?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/invoices/admin_order_invoice.blade.php ENDPATH**/ ?>