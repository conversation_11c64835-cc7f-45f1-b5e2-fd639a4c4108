@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/index_page.css')}}">
@endsection

@section('content')

<x-admin.table 
    datefilter="true" 
    order="true" 
    addbutton="{{ route('admin.fqs.create') }}" 
    deletebutton="{{ route('admin.fqs.deleteAll') }}" 
    :searchArray="[
        'question' => [
            'input_type' => 'text' , 
            'input_name' => __('admin.question') , 
        ] ,
        'answer' => [
            'input_type' => 'text' , 
            'input_name' => __('admin.answer') , 
        ] ,
        'audience_type' => [
            'input_type' => 'select' ,
            'input_name' => __('admin.audience_type') ,
                    'rows' => [
                        'client' => [
                            'name' => __('admin.client') ,
                            'id' => 'client' ,
                        ],
                        'provider' => [
                            'name' => __('admin.provider') ,
                            'id' => 'provider' ,
                        ],
                    ] ,
                ] ,
            ]" 
>
    <x-slot name="tableContent">
        <div class="table_content_append card">

        </div>
    </x-slot>
</x-admin.table>


    
@endsection

@section('js')

    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>

    {{-- delete all script --}}
        @include('admin.shared.deleteAll')
    {{-- delete all script --}}

    {{-- delete one user script --}}
        @include('admin.shared.deleteOne')
    {{-- delete one user script --}}

    {{-- delete one user script --}}
        @include('admin.shared.filter_js' , [ 'index_route' => url('admin/fqs')])
    {{-- delete one user script --}}
@endsection
