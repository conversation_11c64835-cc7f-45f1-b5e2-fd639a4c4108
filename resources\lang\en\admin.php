<?php
return [
    "welcome"                                                => "Welcome",
    "address"                                                => " the address",
    "weather"                                                => "the weather",
    "riyadh"                                                 => "Riyadh",
    "induction_Statistics"                                   => "Website stats",
    "country_and_cities"                                     => "countries and cities",
    "Project_cases"                                          => "Project Cases",
    "users"                                                  => "Users",
    'published_at' => 'published at',
    'rates' => 'rates',
    'freelancer' => 'freelancer',
    'portfolio' => 'Portfolio',
    'id_number' => 'ID Number',
    'id_image' => 'ID Image',
    'freelance_document_image' => 'Freelance Document Image',
    'past_work' => 'Past Work',
    "active_users"                                           => " Active users ",
    "dis_active_users"                                       => " Inactive users ",
    "Prohibited_users"                                       => "Banned Users",
    "Unspoken_users"                                         => " Unblocked users ",
    "active_customers"                                       => "Active customers",
    "dis_active_customers"                                   => "Inactive clients",
    "questions_sections"                                     => "common questions",
    "add_question"                                           => "add a question",
    "edit_question"                                          => "Edit question",
    "view_question"                                          => "Show a question",
    "question"                                               => "the question",
    "question_in_arabic"                                     => "The question is in Arabic",
    "question_in_english"                                    => "The question is in English",
    "answer"                                                 => "the answer",
    "add_a_banner"                                           => "Add an ad banner",
    "modification_of_banner"                                 => "Edit my ad banner",
    "modification_of_services"                               => " Edit service ",
    "view_of_services"                                       => " service offer ",
    "view_of_banner"                                         => "Display ad banner",
    "image"                                                  => "Image",
    "app_setting"                                            => "Application settings ",
    "terms_and_conditions"                                   => "Terms and Conditions",
    "conditions_and_conditions_in_arabic"                    => "Terms and Conditions in Arabic",
    "conditions_and_conditions_of_english"                   => "Terms and Conditions in English",
    "Privacy_policy"                                         => "privacy policy",
    "email_data"                                             => "email data ",
    "notification_data"                                      => "Notification data ",
    "api_data"                                               => "data api ",
    "the_name_of_the_application_in_arabic"                  => "The name of the application is in Arabic",
    "the_name_of_the_application_in_english"                 => "The name of the application is in English",
    "whatts_app_number"                                      => "WhatsApp number",
    "about_app"                                              => "About the app ",
    "about_the_application_in_arabic"                        => "About the application in Arabic",
    "about_the_application_in_english"                       => "About the application in English",
    "privacy_policy_in_arabic"                               => "Application privacy policy in Arabic",
    "privacy_policy_in_english"                              => "Application privacy policy in English",
    "cancel_policy_in_ar"                                => "Application cancellation policy in Arabic",
    "cancel_policy_in_en"                               => "Application cancellation policy in English",
    "cancel_policy"                                          => "Cancellation policy",
    "audience_type"                                          => "Audience type",
    "email_Sender"                                           => "Sent email",
    "the_sender_name"                                        => "The sender's name ",
    "the_nouns_al"                                           => "Host name ",
    "encryption_type"                                        => "Encryption type",
    "Port_number"                                            => "port number",
    "server_key"                                             => "server key",
    "sender_identification"                                  => "Sender ID",
    "live_chat"                                              => "live chat",
    "google_analytics"                                       => "Google Analytics",
    "google_places"                                          => "google places",
    "verify_the_email_format"                                => "Verify the email format",
    "add_a_way_to_work"                                      => "Add a working method",
    "edit_a_way_to_work"                                     => "Modify the way it works",
    "view_a_way_to_work"                                     => "Show how it works",
    "Frequent_texts"                                         => "repetitive texts",
    "name_of_sender"                                         => "The sender's name",
    "show_message"                                           => " View message",
    "no_salon_images_uploaded" => 'no previous images',
    "text_of_message"                                        => " Message text",
    "name_of_induction_in_arabic"                            => "Arabic site name",
    "name_of_the_induction_of_english"                       => "The name of the website in English",
    "the_number_should_only_be_less_than_ten_numbers"        => "Phone number must be at least 10 numbers ",
    "The_main_website_color"                                 => "main site color",
    "the_color_of_the_buttons"                               => "color buttons",
    "color_of_hover"                                         => "color hover",
    'platform_commission' => 'Total platform commission',
    'total_revenue' => 'Total revenue',
    'total_discounts' => 'Total discounts',
    'currnet_provider_share' => 'Current provider share',
    'total_provider_share' => 'Total provider share',
    'booking_fees' => 'Total booking fees',
    'total_clients' => 'Total clients',
    'total_deleted_providers' => 'Total deleted providers',
    'total_deleted_clients' => 'Total deleted clients',
    'total_provider_deletion_requests' => 'Total provider deletion requests',
    'total_blocked_providers' => 'Total blocked providers',
    'total_active_providers' => 'Total active providers',
    'total_inactive_providers' => 'Total inactive providers',
    "logo_image_induction"                                   => "Website logo image",
    "logo_image"                                             => "website logo image ",
    "fav_icon_image"                                         => " picture fav icon ",
    "background_image"                                       => "Login page background image",
    "virtual_user_image"                                     => "default user image",
    "Picture_of_Loader"                                      => " picture loader ",
    "add_a_profile_page"                                     => "Add a profile page",
    "edit_a_profile_page"                                    => "Edit profile page",
    "view_a_profile_page"                                    => "Show profile page",
    "description"                                            => " the description",
    "saving_changes"                                         => "Saving changes",
    "notification_address_in_arabic"                         => "The title of the notice in Arabic",
    "notification_address_in_english"                        => "Notice title in English",
    "the_written_text_of_the_email"                          => "The written text of the email",
    "the_written_text_of_the_sms"                            => "The written text of the sms",
    "address_of_our_services_section_in_arabic"              => "The address of our services section in Arabic",
    "the_title_of_our_english_service_department"            => "The address of our services section in English",
    "the_title_of_how_the_site_works_in_arabic"              => "The title of the section How the site works in Arabic",
    "the_title_of_the_section_of_how_the_english_site_works" => "The title of the section How does the site work in English",
    "the_address_of_the_questions_section_in_arabic"         => "The title of the FAQ section in Arabic",
    "the_address_of_the_questions_section_english"           => "English FAQ section title",
    "the_title_of_our_partition_in_arabic"                   => "Our partners section address in Arabic",
    "the_title_of_our_english_partition"                     => "Our Partners section in English",
    "address_in_arabic_communication"                        => "Arabic communication section title",
    "address_in_english_communication"                       => "English communication section title",
    "image_of_the_first_application"                         => "A picture of the first application",
    "Picture_of_the_second_application"                      => "A picture of the second application",
    "about_the_arabic_application"                           => "About the application in Arabic",
    "about_the_english_application"                          => "About the application in English",
    "add_partner"                                            => "Add a partner",
    "edit_partner"                                           => "Edit partner",
    "view_partner_success"                                   => "Show partner success ",
    "date"                                                   => "Date",
    "activation"                                             => "activation",
    "activate"                                               => "Enabled",
    "dis_activate"                                           => "Not enabled",
    "add_admin"                                              => " add admin",
    "write_the_name"                                         => "write the name",
    "this_field_is_required"                                 => "This field is required",
    "name"                                                   => "The name",
    "user_name"                                              => "user name",
    "phone"                                                  => "Telephone number",
    "status"                                                 => "the condition",
    "add"                                                    => "addition",
    'total_consultation_messages' => 'Total consultation messages',
    'withdrawable_balance' => 'Withdrawed Balance',
'current_balance' => 'Current Balance',
'saudi' => 'Saudi',
    'other' => 'other',
    'provider_suborders_total_sum' => 'total_payments',
    'processed_by' => 'Processed by',
    'course_information' =>'course information',
    'client_information' => 'client information',

    'cuurent_worth_balance' => 'current worthy balance',
    "update"                                                 => "Modify",
    "update_successfullay"                                   => "Edited successfully",
    "sort_by"                                                => "sort by",
    "404_the_page_does_not_exist"                            => "404 Page not found",
    "back_to_home_page"                                      => "Back to the home page",
    "Progressive"                                            => "Progressive",
    "descending"                                             => "descending",
    "beginning_date"                                         => "The beginning of history",
    "end_date"                                               => "end of history",
    "back"                                                   => "back",
    "Validity"                                               => "validity",
    "Validities"                                             => "powers",
    "Select_the_validity"                                    => "Choose the validity",
    "Select_the_blocking_status"                             => "Choose Ban Status",
    "ban_status"                                             => "ban status",
    "phone_activation_status"                                => "phone activation status",
    "Prohibited"                                             => "forbidden",
    "Unspoken"                                               => "not prohibited",
    "Send_notification"                                      => "send notice",
    "Send_email"                                             => "Send an email",
    "active"                                                 => "active",
    "password"                                               => "The password",
    "incorrect_password"                                     => "The password is incorrect",
    "email"                                                  => "E-mail",
    "enter_the_email"                                        => "write email",
    "enter_the_phone"                                        => "Write your phone number",
    "email_formula_is_incorrect"                             => "The email format is incorrect",
    "phone_number"                                           => "Telephone number",
    "enter_phone_number"                                     => " Write your phone number",
    "edit_admin"                                             => "admin edit",
    "edit_client"                                            => "Edit client",
    "select_all"                                             => "select all",
    "there_is_no_data_at_the_moment"                         => "There is no data at the moment",
    "confirm"                                                => "emphasis",
    "send"                                                   => "send",
    "cancel"                                                 => "Cancellation",
    "the_selected_has_been_successfully_deleted"             => "Selector has been successfully deleted",
    "delete_selected"                                        => "delete selected",
    "view_admin"                                             => "admin show",
    "view_user"                                              => "View user",
    "image"                                                  => "Image",
    "control"                                                => "control",
    "last_message"                                           => "Last Message",
    "last_message_date"                                      => "Last Message Date",
    "no_messages"                                            => "No Messages",
    "there_are_no_matches_matching"                          => "There are no matching results",
    "login"                                                  => "Login",
    "section"                                                => " Section",
    "add_section"                                            => "Add a section",
    "edit_section"                                           => "Edit Section",
    "select_section"                                         => "Choose the section",
    "select_main_section"                                    => " Choose the main section",
    "select_as_main_section"                                 => "Select as main",
    "type"                                                   => "Type",
    "view_section"                                           => "Show section",
    "view_sub_sections"                                      => "Show subcategories",
    "view"                                                   => "Show",
    "loading"                                                => "Loading",
    "add_city"                                               => "Add a city",
    "edit_city"                                              => "Edit City",
    "view_city"                                              => "View city",
    "add_country"                                            => "add country",
    "edit_country"                                           => "Edit country",
    "view_country"                                           => "Show country",
    "country"                                                => "Country",
    "countries"                                              => "Countries",
    "cities"                                                 => "cities",
    "country_code"                                           => " country code",
    "enter_country_code"                                     => " Type the country code",
    "choose_the_country"                                     => "Choose the country",
    "add_client"                                             => "add customer",
    "the_sender_name"                                        => "The sender's name",
    "complaining"                                            => "the complaint",
    "email_to_complain"                                      => "Email of the complainant",
    "name_to_complain"                                       => "Complainant's name",
    "phone_to_complain"                                      => "Complainant's phone number",
    "replay"                                                 => "to reply",
    "the_replay"                                             => "reply",
    "replay_successfullay"                                   => "Successfully answered",
    "the_resolution_of_complaining_or_proposal"              => "View your complaint or suggestion",
    "add_coupons"                                            => "Add a discount coupon",
    "edit_coupons"                                           => "Edit discount coupon",
    "view_coupons"                                           => "Coupon show ",
    "coupon_number"                                          => "coupon number",
    "coupon_number_used_before"                              => "Coupon number is already used",
    "update_coupon_status"                                   => "Coupon status update",
    "update_coupon_status_successfully"                      => "Coupon status updated successfully",
    "active_or_diactive_coupon"                              => "Activate or deactivate a coupon",
    "reactivation_Coupon"                                    => "Reactivate the coupon",
    "Stop_Coupon"                                            => "stop coupon",
    "number_of_use"                                          => "Frequency of use",
    "enter_number_of_use"                                    => " Type the number of times you use it",
    "enter_coupon_number"                                    => " Enter the coupon number",
    "discount_type"                                          => "Discount type",
    "select_the_discount_state"                              => "Choose discount status",
    "Percentage"                                             => "Rate",
    "Percentage_must_not_bigger_than_100"                    => "The percentage cannot be greater than 100",
    "fixed_number"                                           => "fixed number",
    "discount_value"                                         => "discount value",
    "type_the_value_of_the_discount"                         => "Enter the discount value",
    "larger_value_for_discount"                              => "Biggest discount value",
    "write_the_greatest_value_for_the_discount"              => "Enter the maximum value of the discount",
    "expiry_date"                                            => "Expiry date",
    "the_phone_number_ must_not_have_charachters_or_symbol"  => "The phone number must not contain any letters or symbols",
    "just_allows_the_numbers"                                => "Only numbers are allowed",
    "add_services"                                           => "add service",
    "socials"                                                => " Means of communication",
    "add_socials"                                            => "Add a contact",
    "edit_socials"                                           => "Edit contact",
    "view_socials"                                           => "Show contact",
    "name_of_socials"                                        => "Contact name",
    "name_of_website"                                        => "Web site name",
    "Link"                                                   => "Link",
    "enter_the_link"                                         => " write the link",
    "icon"                                                   => "icon",
    "enter_the_icon"                                         => " write the icon",
    "text_of_icon"                                           => " Icon text",
    "admins"                                                 => "Supervisors",
    "complaints_and_proposals"                               => "Complaints and suggestions",
    "reports"                                                => "Reports ",
    "common_questions"                                       => "common questions",
    "definition_pages"                                       => "profile pages",
    "advertising_banners"                                    => "Ad banners",
    "message_packages"                                       => "Message packages",
    "insolder"                                               => " Slider banners",
    "Service_Suite"                                          => "section our services",
    "Common-questions_sections"                              => "FAQ Sections ",
    "Success_Partners"                                       => "Success Partners",
    "Customer_messages"                                      => "Customer Messages",
    "how_the_site_works_section"                             => "section how we work",
    "login_successfully_logged"                              => "You are logged in successfully",
    "Copyrights"                                             => "Copyrights",
    "all_rights_reserved"                                    => "All rights reserved",
    "tocaan"                                                 => "Tocaan",
    "available"                                              => "Available",
    "send_notification"                                      => "send notice",
    "send_sms"                                               => " send SMS ",
    "send_email"                                             => "Send an email",
    "the_message_in_arabic"                                  => "The message is in Arabic",
    "the_message_in_english"                                 => "The message is in English",
    "the_title_in_arabic"                                    => "The title is in Arabic",
    "the_title_in_english"                                   => "The title is in English",
    "send_to"                                                => "send to ",
    "Select_the_senders_category"                            => "Choose the category to which it is sent",
    "all_users"                                              => "all users",
    "email_content"                                          => "Email content",
    "send_successfully"                                      => "sent succesfully",
    "report_text"                                            => "Report text",
    "ip"                                                     => " The ip",
    "show_report"                                            => " View report",
    "subject_of_report"                                      => " The subject of the report",
    "action_type"                                            => "Action type",
    "the_admin"                                              => "admin",
    "browser"                                                => "Browser",
    "*"                                                      => "*",
    "press_here"                                             => "Press here",
    "add_role"                                               => "add validity",
    "edit_role"                                              => "Modify the validity",
    "name_of_role"                                           => "Validity name",
    "add_seo"                                                => " addition seo",
    "edit_seo"                                               => " Modify seo",
    "view_seo"                                               => " Show seo",
    "key"                                                    => " a key ",
    "the_key"                                                => " The key ",
    "write_key"                                              => "type key",
    "Settlement_request"                                     => "settlement request",
    "you_are_about_to_decline_the_settlement_request"        => "You are about to decline the settlement request ؟",
    "receipt_photo"                                          => "receipt photo",
    "settlement_amount"                                      => "settlement amount",
    "View_copy"                                              => "View copy",
    "add_copy"                                               => "add a copy",
    "edit_copy"                                              => "edit version",
    "confirm"                                                => " confirm",
    "close"                                                  => " Close",
    "the_amount"                                             => "the amount",
    "settlement_request_completed_successfully"              => "Settlement request completed successfully",
    "service_provider_name"                                  => "Service Provider Name",
    "order_status"                                           => "Order status",
    "view_order"                                             => "View order",
    "accept_order"                                           => " Request Accept",
    "refuse_order"                                           => " Rejection of the application",
    "order_procedures"                                       => "Order procedures",
    "search"                                                 => "Search",
    "search_in_map"                                          => "Search on the map",
    "enter"                                                  => "Enter",
    "enter_addresss"                                         => "Enter the address",
    "choose"                                                 => "Choose ",
    "do_you_want_to_delete_the_item"                         => "Do you want to delete the item",
    "the_item_was_deleted"                                   => " Item has been deleted",
    "an_error_occurred"                                      => "an error occurred",
    "are_you_want_to_continue"                               => "do you want to continue ؟",
    "are_you_sure_you_want_to_complete_deletion"             => "Are you sure you want to complete the deletion process",
    "added_successfully"                                     => "Added successfully",
    'pay' => 'pay order',
    'wallets_report' => 'clients report logs',
    "the_package_has_been_successfully_activated"            => "The package has been successfully activated",
    "modernization"                                          => "Modernization",
    "type_the_name_of_the_sender"                            => "Type the sender's name",
    "type_the_password"                                      => "Type the password ",
    "type_the_user_name"                                     => "Type the username",
    "introductory_site"                                      => "Introductory site ",
    "introductory_site_setting"                              => "introductory site setting",
    "main_page"                                              => "Main page",
    "view_of_banner_page"                                    => " View  banner page",
    "edit_of_banner_page"                                    => "Edit  banner page",
    "add_of_banner_page"                                     => "Add of banner page",
    "delete_a_banner"                                        => "Delete banner",
    "delete_multible_banner"                                 => "Delete multible banner",
    "our_services"                                           => "Our services ",
    "view_services"                                          => " View services",
    "edit_services"                                          => " Edit services",
    "delete_services"                                        => " Delete services",
    "delete_multible_services"                               => " Delete multible services ",
    "edit_section_page"                                      => "Edit section page",
    "view_section_page"                                      => "View section page",
    "delete_section"                                         => "Delete section",
    "delete_multible_section"                                => "Delete multible section",
    "delete_question"                                        => "Delete question ",
    "delete_multible_question"                               => "Delete multible question",
    "delete_partner"                                         => "Delete_partner",
    "delete_multible_partner"                                => "Delete multible partner",
    "view_message"                                           => "View_message",
    "delete_message"                                         => " Delete message",
    "delete_multible_message"                                => "Delete multible message",
    "delete_socials"                                         => "Delete_socials",
    "delete_multible_socials"                                => "Delete multible socials",
    "how_the_site_works"                                     => "How We Works",
    "delete_user"                                            => "Delete user",
    "delete_multible_user"                                   => "Delete multible user",
    "Send_user_notification"                                 => "Send user notification",
    "notification_page"                                      => "Notification page",
    "delete_notification"                                    => "Delete notification",
    "delete_admin"                                           => "Delete admin",
    "delete_multible_admin"                                  => "Delete multible admin",
    "notifications"                                          => "Notifications",
    "send_notification_email_to_client"                      => " Send notification and email to client",
    "delete_country"                                         => "Delete country",
    "delete_multible_country"                                => "Delete multible country",
    "delete_city"                                            => "Delete city",
    "delete_multible_city"                                   => "Delete multible city",
    "sections"                                               => "Sections",
    "export"                                                 => "Export",
    "coupons"                                                => "Coupons",
    "delete_coupons"                                         => " Delete coupon",
    "delete_multible_coupons"                                => 'Delete multible coupons',
    "delete_a_profile_page"                                  => "Delete a profile page",
    "delete_amultible__profile_page"                         => "Delete a multible profile page",
    "the_replay_of_complaining_or_proposal"                  => "The replay of complaining or proposal",
    "delete_complaining"                                     => "Delete_complaining ",
    "delete_multibles_complaining"                           => " Delete multibles complaining",
    "seo"                                                    => "Seo",
    "delete_seo"                                             => "Delete seo",
    "delete_multible_seo"                                    => "Delete multible seo",
    "message_update"                                         => "Message update",
    "Statistics"                                             => "Statistics",
    "delete_report"                                          => "Delete report",
    "delete_multible_report"                                 => "Delete multible report",
    "Validities_list"                                        => "Validities list",
    "delete_role"                                            => "Delete role",
    "setting"                                                => "Setting",
    "edit_setting"                                           => "Edit setting",
    "message_all"                                            => "Message all",
    "message_one"                                            => "Message one",
    "send_email"                                             => "Send email",
    "view_Settlement_order"                                  => "View Settlement order",
    "Settlement_requests"                                    => "Settlement requests",
    "change_status_Settlement_requests"                      => "Change status of Settlement requests",
    "admin_financial_reports"                                => "Financial Reports",
    "order_num"                                              => "Number of order",
    "debit"                                                  => "Debit",
    "credit"                                                 => "Credit",
    "transactionable_type"                                   => "Transactionable type",
    "orders"                                                 => "Orders",
    "wallet_details"                                          => "Wallet details",
    "block"                                          => "block",
    "unblock"                                          => "un block",
    "client_blocked"                                          => " Blocked successfully",
    "client_unblocked"                                          => " Un Blocked successfully",
    "block_admin"                                          => "Block Admin",
    "add"               => 'ِAdd' ,
    "edit"               => 'Edit' ,
    "show"               => 'Show' ,

    'region'                                                 => 'region',
    'regions'                                                => 'regions',
    'add_region_page'                                        => 'add region page',
    'add_region'                                             => 'add region',
    'update_region_page'                                     => 'update region page',
    'update_region'                                          => ' update region',
    'show_region_page'                                       => 'show region page',
    'delete_region'                                          => ' delete region',
    'delete_group_of_regions'                                => 'delete multiple regions',

    'importfile'                                             => 'import',
    'upload'                                                 => 'upload',
    'uploaded_successfully'                                  => 'uploaded successfully',

    'cover'      => 'cover',
    'birth_date'        => 'birth date',
    'identity_image'        => 'identity image',
    'update_balance'        => 'Edit Balance',
    'data_user'             => 'User Info',
    'wallet_history'        => 'Wallet History',
    'add_or_deduct_balance'  => 'Add and deduct balance',
    'enter_add_or_deduct_balance'  => 'Enter Balance',
    'wallet'                => 'Wallet',
    'balance'               => 'Balance',
    'send_notify'           => 'Send Notification',
    'opertaion'             => 'Opertaion',
    'write'                => 'Write',

    'clients'    => 'clients',
    'countries_cities'   => 'Regions & Cities',
    'marketing'      => 'Marketing',
    'all_settings'  => 'Settings',
    'delete_multible_question' => 'Delete Multiple Questions',
    'delete_multible_report'  => 'Delete Multiple Operations',
    'reports'        => 'Logs',
    'project'         => 'Project',
    'ar'             => 'Arabic Info',
    'en'             => 'English Info',
    'in_ar'          => 'in Arabic',
    'in_en'          => 'in English',
    'choose_the_region'   => 'Choose The Region',
    'select_region'       => 'Select Region',
    'Region'              => 'Region',
    'flag'          => 'Country Flag',
    'withdrawal_settings' => 'Withdrawal Settings',
    "comission_withdrawal_fee" => "Comission Withdrawal Fee",
    'language_setting'  => 'Language Settings',
    'supported_languages' => 'Supported Languages',
    'rtl_languages' => 'Right To Lift Languages',
    'default_language' => 'Main Language',
    'data'    => 'Data',

    // Services translations
    'services'                      => 'Services',
    'service'                       => 'Service',
    'add_service'                   => 'Add Service',
    'edit_service'                  => 'Edit Service',
    'show_service'                  => 'Show Service',
    'delete_service'                => 'Delete Service',
    'service_name'                  => 'Service Name',
    'service_description'           => 'Service Description',
    'service_price'                 => 'Service Price',
    'service_duration'              => 'Service Duration',
    'expected_time_to_accept'       => 'Expected Time to Accept',
    'service_category'              => 'Service Category',
    'service_provider'              => 'Service Provider',
    'service_status'                => 'Service Status',
    'active_services'               => 'Active Services',
    'inactive_services'             => 'Inactive Services',
    'minutes'                       => 'Minutes',
    'hours'                         => 'Hours',
    'duration_in_minutes'           => 'Duration in Minutes',
    'expected_time_in_minutes'      => 'Expected Time in Minutes',
    'select_provider'               => 'Select Provider',
    'select_category'               => 'Select Category',
    'no_provider'                   => 'No Provider',
    'no_category'                   => 'No Category',
    'category_not_found'            => 'Category not found',
    'all'                           => 'All',
    'add'                           => 'Add',
    'not_set'                       => 'Not Set',
    'choose'                        => 'Choose',
    'sar'                           => 'SAR',
    'no_name'                       => 'No Name',
    'product_name'                  => 'Product Name',
    'product_description'           => 'Product Description',
    'product_provider'              => 'Product Provider',
    'product_category'              => 'Product Category',
    'product_price'                 => 'Product Price',
    'product_quantity'              => 'Product Quantity',
    'add_product'                   => 'Add Product',
    'edit_product'                  => 'Edit Product',
    'show_product'                  => 'Show Product',
    'delete_product'                => 'Delete Product',
    'products'                      => 'Products',
    'no_provider'                   => 'No Provider',
    'no_category'                   => 'No Category',

    // Route title translations
    'providers'                     => 'Providers',
    'pending_provider_requests'     => 'Pending Provider Requests',
    'add_provider'                  => 'Add Provider',
    'edit_provider'                 => 'Edit Provider',
    'view_provider'                 => 'View Provider',
    'delete_provider'               => 'Delete Provider',
    'delete_multiple_providers'     => 'Delete Multiple Providers',
    'notify_providers'              => 'Notify Providers',
    'update_provider_balance'       => 'Update Provider Balance',
    'approve_provider_request'      => 'Approve Provider Request',
    'reject_provider_request'       => 'Reject Provider Request',
    'change_provider_status'        => 'Change Provider Status',
    'provider_client'               => 'Provider Client',
    'profile_image' => 'profile image' , 

    'account_deletion_requests'     => 'Account Deletion Requests',
    'view_deletion_request'         => 'View Deletion Request',
    'approve_deletion_request'      => 'Approve Deletion Request',
    'reject_deletion_request'       => 'Reject Deletion Request',

    'all_orders'                    => 'All Orders',
    'show_orders_page'              => 'Show Orders Page',
    "fees_settings" => "Fees Settings",
    "fees_settings_desc" => "Control all fees applied in the application",
    "delivery_fees" => "Delivery Fees",
    "delivery_fees_desc" => "Product delivery fees, set by the admin, refundable upon cancellation",
    "normal_delivery_fee_hint" => "Normal delivery fee",
    "express_delivery_fee_hint" => "Express delivery fee",
    'mark_payment_as_paid'          => 'Mark Payment as Paid',
    'update_provider_status'        => 'Update Provider Status',

    'bank_transfer_orders'          => 'Bank Transfer Orders',
    'show_bank_transfer_orders_page' => 'Show Bank Transfer Orders Page',
    'verify_bank_transfer'          => 'Verify Bank Transfer',
    'reject_bank_transfer'          => 'Reject Bank Transfer',

    'cancel_request_orders'         => 'Cancel Request Orders',
    'show_cancel_request_orders_page' => 'Show Cancel Request Orders Page',
    'accept_cancel_request'         => 'Accept Cancel Request',
    'reject_cancel_request'         => 'Reject Cancel Request',

    'paymentmethods'                => 'Payment Methods',
    'show_paymentmethod_page'       => 'Show Payment Method Page',
    'delete_paymentmethod'          => 'Delete Payment Method',
    'delete_group_of_paymentmethods' => 'Delete Group of Payment Methods',

    'product_categories'            => 'Product Categories',
    'add_product_category_page'     => 'Add Product Category Page',
    'add_product_category'          => 'Add Product Category',
    'update_product_category_page'  => 'Update Product Category Page',
    'update_product_category'       => 'Update Product Category',
    'show_product_category_page'    => 'Show Product Category Page',
    'delete_product_category'       => 'Delete Product Category',
    'delete_group_of_product_categories' => 'Delete Group of Product Categories',

    'blog_categories'               => 'Blog Categories',
    'add_blogcategory_page'         => 'Add Blog Category Page',
    'add_blogcategory'              => 'Add Blog Category',
    'update_blogcategory_page'      => 'Update Blog Category Page',
    'update_blogcategory'           => 'Update Blog Category',
    'show_blogcategory_page'        => 'Show Blog Category Page',
    'delete_blogcategory'           => 'Delete Blog Category',
    'delete_group_of_blogcategories' => 'Delete Group of Blog Categories',

    'blogs'                         => 'Blogs',
    'add_blog_page'                 => 'Add Blog Page',
    'add_blog'                      => 'Add Blog',
    'update_blog_page'              => 'Update Blog Page',
    'update_blog'                   => 'Update Blog',
    'show_blog_page'                => 'Show Blog Page',
    'delete_blog'                   => 'Delete Blog',
    'delete_group_of_blogs'         => 'Delete Group of Blogs',
    'load_more_comments'            => 'Load More Comments',
    'toggle_comment_approval'       => 'Toggle Comment Approval',

    'courses'                       => 'Courses',
    'add_course_page'               => 'Add Course Page',
    'add_course'                    => 'Add Course',
    'edit_course_page'              => 'Edit Course Page',
    'edit_course'                   => 'Edit Course',
    'show_course_page'              => 'Show Course Page',
    'delete_course'                 => 'Delete Course',
    'delete_multible_course'        => 'Delete Multiple Courses',
    'toggle_course_status'          => 'Toggle Course Status',

    'course_enrollments'            => 'Course Enrollments',
    'view_course_enrollments'       => 'View Course Enrollments',
    'delete_course_enrollment'      => 'Delete Course Enrollment',
    'delete_multible_course_enrollments' => 'Delete Multiple Course Enrollments',

    'add_product_page'              => 'Add Product Page',
    'update_product_page'           => 'Update Product Page',
    'update_product'                => 'Update Product',
    'show_product_page'             => 'Show Product Page',
    'delete_group_of_products'      => 'Delete Group of Products',
    'toggle_product_status'         => 'Toggle Product Status',

    'delete_multible_services'      => 'Delete Multiple Services',
    'toggle_service_status'         => 'Toggle Service Status',

    'change_client_status'          => 'Change Client Status',
    'update_balance'                => 'Update Balance',
    'block_client'                  => 'Block Client',
    'delete_multible_user'          => 'Delete Multiple Users',

    // Management groups
    'products_management'           => 'Products Management',
    'services_management'           => 'Services Management',
    'blogs_management'              => 'Blogs Management',
    'courses_management'            => 'Courses Management',
    'question'    => 'Question',
    'answer'    => 'Answer',
    'title'     => 'Title',
    'from_date'  => 'From date',
    'to_date'    => 'To date',
    "is_production" => "Production Mode",
    "registeration_availability" => "Delivery Registration Availability",
    "vat_amount" => "VAT Amount",
    "vat_amount_help" => "Enter the VAT percentage (e.g., 15)",
    "loyalty_points_settings" => "Loyalty Points Settings",
    "loyalty_points_description" => "Enable and manage the customer loyalty points system.",
    "enable_loyalty_points" => "Enable Loyalty Points",
    "points_earned_per_sar" => "Points Earned per SAR",
    "points_earn_example" => "Example: 1 SAR = 1 point",
    "point_value_in_sar" => "Point Value in SAR",
    "point_value_example" => "Example: 1 point = 0.1 SAR",
    "min_points_to_redeem" => "Minimum Points to Redeem",
    'mark_as_read' => 'Mark as Read',
    "suggestion" => "Suggestion",
    "complaint" => "Complaint",
    'user_type' => 'User Type',
    "min_points_hint" => "Customer cannot redeem less than this amount.",
    "max_points_percentage" => "Maximum Points Percentage Redeemable per Order (%)",
    "max_points_hint" => "Example: If 50%, customer can pay half the order value with points only.",
    "loyalty_notes_title" => "Loyalty Points Notes",
    "note_points_on_payment" => "Points are only calculated upon full payment.",
    "note_used_points_no_earn" => "When using points for payment, no new points are earned on that part.",
    "note_earned_from_paid_amount" => "Points are calculated only from the amount paid in cash or electronically.",
    "normal_delivery_fee" => "Normal Delivery Fee (SAR)",
    "express_delivery_fee" => "Express Delivery Fee (SAR)",
    "service_commission" => "Platform Commission from Services (SAR)",
    "product_commission" => "Platform Commission from Products (SAR)",
    "service_referral_commission" => "Referral Commission for Services (SAR)",
    "product_referral_commission" => "Referral Commission for Products (SAR)",
    'link'       => 'Link',
    'view_a_profile_page'  => 'View Intro Page',
    'add_a_profile_page'   => 'Add Intro Page',
    'edit_a_profile_page'  => 'Update Intro Page',
    'delete_a_profile_page' => 'Delete Intro Page',
    'delete_amultible_profile_page' => 'Delete Multible Intro Pages',
    'the_title' => 'The Title',
    'the_message'  => 'The Message',
    'start_date'   => 'Start Date',
    'notify'    => 'Notify',
    'delete'    => 'Delete',
    'countries_currencies' => 'Countries & Currencies',
    'supported_countries'  => 'Supported Countries',
    'default_country'      => 'Default Country',
    'supported_currencies'  => 'Supported Currencies',
    'default_currency'     => 'Default Currency',
    'currency'  => ' Currency',
    'currency_code'  => 'Currency Code',
    'forget_password'  => 'Forget Password?',
    'forget_password_text' => "Enter your email and we'll send you code to reset your password",
    'back_login' => 'Back to login',
    'send_code'   => 'Send Code',
    'code_send'   => 'Code Sent Successfully',
    'reset_password'  => 'Reset Password',
    'reset_password_text' => 'Enter the received code then enter new password',
    'password_confirmation' => 'Password Confirmation',
    'reset' => 'Reset',
    'passwordChanges' => 'Password Reset Successfully',
    'code_wrong'   => 'this code is wrong',
    'about_the_application' => 'About App',
    'privacy_policy'        => 'Privacy And Policy',
    'conditions_and_conditions' => 'Terms And Conditions',
    'clientss' => 'users',

    // Product related translations
    'price' => 'Price',
    'discount_price' => 'Discount Price',
    'stock' => 'Stock',
    'category' => 'Category',
    'select_category' => 'Select Category',
    'is_active' => 'Is Active',
    'inactive' => 'Inactive',
    'no_category' => 'No Category',
    'write_title' => 'Write Title',
    'write_description' => 'Write Description',
    'products' => 'products',
    'deliveries' =>'Deliveries',
    'add_delivery' => 'Add delivery',
    'edit_delivery' => 'Edit Delivery',
    'paymentmethod'                        => 'Payment Method',
    'paymentmethods'                       => 'Payment Methods',
    'add_paymentmethod_page'               => 'Add Payment Method Page',
    'add_paymentmethod'                    => 'Add Payment Method',

    // Gifts related translations
    'gifts'                                => 'Gifts',
    'gift'                                 => 'Gift',
    'add_gift'                             => 'Add Gift',
    'edit_gift'                            => 'Edit Gift',

    // Orders management translations
    'orders_management'                    => 'Orders Management',
    'all_orders'                          => 'All Orders',
    'bank_transfer_orders'                => 'Bank Transfer Orders',
    'regular_orders'                      => 'Regular Orders',
    'manage_all_orders_from_one_place'    => 'Manage all orders from one place',
    'regular_orders_coming_soon'          => 'Regular Orders Coming Soon',
    'regular_orders_description'          => 'This section will show orders excluding bank transfers',
    'bank_transfer_status'                => 'Bank Transfer Status',
    'verify_transfer'                     => 'Verify Transfer',
    'reject_transfer'                     => 'Reject Transfer',
    'are_you_sure_verify_transfer'        => 'Are you sure you want to verify this transfer?',
    'are_you_sure_reject_transfer'        => 'Are you sure you want to reject this transfer?',
    'verify'                              => 'Verify',
    'reject'                              => 'Reject',
    'notes_optional'                      => 'Notes (Optional)',
    'notes'                               => 'Notes',
    'rejection_reason'                    => 'Rejection Reason',
    'reason'                              => 'Reason',
    'rejection_reason_required'           => 'Rejection reason is required',
    'success'                             => 'Success',
    'error'                               => 'Error',
    'something_went_wrong'                => 'Something went wrong',
    'provider_orders_status_history'      => 'Provider Orders Status History',
    'provider_sub_orders_audit_trail'     => 'Track status changes for each provider sub-order',
    'sub_order'                           => 'Sub Order',
    'no_status_changes_for_provider'      => 'No status changes for this provider',
    'no_provider_orders'                  => 'No Provider Orders',
    'provider_orders_will_appear_here'    => 'Provider orders will appear here when available',
    'customer'                            => 'Customer',

    // Cancel request orders translations
    'cancel_request_orders'               => 'Cancel Request Orders',
    'cancel_request_orders_info'          => 'Orders with pending cancellation requests from customers',
    'accept_cancel_request'               => 'Accept Cancel Request',
    'reject_cancel_request'               => 'Reject Cancel Request',
    'are_you_sure_accept_cancel_request'  => 'Are you sure you want to accept this cancellation request?',
    'are_you_sure_reject_cancel_request'  => 'Are you sure you want to reject this cancellation request?',
    'cancel_fees'                         => 'Cancellation Fees',
    'cancel_fees_required'                => 'Cancellation fees are required',
    'cancel_fees_must_be_positive'        => 'Cancellation fees must be positive',
    'cancel_fees_cannot_exceed_total'     => 'Cancellation fees cannot exceed order total',
    'refund_amount'                       => 'Refund Amount',
    'enter_notes'                         => 'Enter additional notes...',
    'cancel_request_pending_review'       => 'Cancellation Request Pending Review',
    'no_reason_provided'                  => 'No reason provided',
    'no_cancel_requests_found'            => 'No cancellation requests found',
    'request_date'                        => 'Request Date',
    'processing'                          => 'Processing',
    'ongoing'                             => 'Ongoing',
    'please_provide_reason'               => 'Please provide a reason',
    'enter_rejection_reason'              => 'Enter rejection reason...',
    'reason_required'                     => 'Reason is required',

    // Order management route titles
    'bank_transfer_orders'                => 'Bank Transfer Orders',
    'show_bank_transfer_orders_page'      => 'Show Bank Transfer Order',
    'verify_bank_transfer'                => 'Verify Bank Transfer',
    'reject_bank_transfer'                => 'Reject Bank Transfer',
    'mark_payment_as_paid'                => 'Mark Payment as Paid',
    'update_provider_status'              => 'Update Provider Status',
    'show_cancel_request_orders_page'     => 'Show Cancel Request Order',
    'accept_cancel_request'               => 'Accept Cancel Request',
    'reject_cancel_request'               => 'Reject Cancel Request',

    // Order related translations
    'order_number'                         => 'Order Number',
    'order_date'                           => 'Order Date',
    'order_details'                        => 'Order Details',
    'basic_information'                    => 'Basic Information',
    'customer_information'                 => 'Customer Information',
    'provider_information'                 => 'Provider Information',
    'address_information'                  => 'Address Information',
    'order_items'                          => 'Order Items',
    'financial_breakdown'                  => 'Financial Breakdown',
    'payment_information'                  => 'Payment Information',
    'bank_transfer_details'                => 'Bank Transfer Details',
    'booking_type'                         => 'Booking Type',
    'delivery_type'                        => 'Delivery Type',
    'scheduled_at'                         => 'Scheduled At',
    'no_customer_info'                     => 'No Customer Information',
    'commercial_name'                      => 'Commercial Name',
    'provider_name'                        => 'Provider Name',
    'provider_type'                        => 'Provider Type',
    'home_and_salon'                       => 'Home & Salon',
    'home_service'                         => 'Home Service',
    'salon_service'                        => 'Salon Service',
    'city'                                 => 'City',
    'location'                             => 'Location',
    'view_on_map'                          => 'View on Map',
    'item_name'                            => 'Item Name',
    'quantity'                             => 'Quantity',
    'unit_price'                           => 'Unit Price',
    'total'                                => 'Total',
    'service'                              => 'Service',
    'product'                              => 'Product',
    'unknown'                              => 'Unknown',
    'subtotal'                             => 'Subtotal',
    'services_total'                       => 'Services Total',
    'products_total'                       => 'Products Total',
    'booking_fee'                          => 'Booking Fee',
    'home_service_fee'                     => 'Home Service Fee',
    'delivery_fee'                         => 'Delivery Fee',
    'discount'                             => 'Discount',
    'loyalty_points_used'                  => 'Loyalty Points Used',
    'payment_method'                       => 'Payment Method',
    'payment_status'                       => 'Payment Status',
    'payment_reference'                    => 'Payment Reference',
    'payment_date'                         => 'Payment Date',
    'coupon_code'                          => 'Coupon Code',
    'sender_bank_name'                     => 'Sender Bank Name',
    'sender_account_holder'                => 'Account Holder Name',
    'sender_account_number'                => 'Account Number',
    'sender_iban'                          => 'IBAN Number',
    'transfer_amount'                      => 'Transfer Amount',
    'transfer_date'                        => 'Transfer Date',
    'transfer_reference'                   => 'Transfer Reference',
    'admin_notes'                          => 'Admin Notes',
    'verify_transfer'                      => 'Verify Transfer',
    'reject_transfer'                      => 'Reject Transfer',
    'back_to_orders'                       => 'Back to Orders',
    'mark_as_processing'                   => 'Mark as Processing',
    'mark_as_completed'                    => 'Mark as Completed',
    'cancel_order'                         => 'Cancel Order',
    'are_you_sure'                         => 'Are you sure?',
    'error_occurred'                       => 'An error occurred',
    'confirm_verify_transfer'              => 'Are you sure you want to verify this transfer?',
    'rejection_reason'                     => 'Rejection Reason',
    'no_provider'                          => 'No Provider',
    'sar'                                  => 'SAR',
    'services'                             => 'Services',
    'products'                             => 'Products',
    'view_details'                         => 'View Details',
    'created_at'                           => 'Created At',
    'wallet_balance'                       => 'Wallet Balance',
    'final_total'                          => 'Final Total',
    'user'                                 => 'User',
    'provider'                             => 'Provider',
    'cancel_reason' => 'Cancel Reason',
    'accept' => 'Accept',


    // Order status translations
    'pending_payment'                      => 'Pending Payment',
    'pending_verification'                 => 'Pending Verification',
    'processing'                           => 'Processing',
    'confirmed'                            => 'Confirmed',
    'completed'                            => 'Completed',
    'cancelled'                            => 'Cancelled',
    'failed'                               => 'Failed',
    'blogs_count'                          => 'Blogs Count',
    'paid'                                 => 'Paid',
    'pending'                              => 'Pending',
    'refunded'                             => 'Refunded',
    'verified'                             => 'Verified',
    'rejected'                             => 'Rejected',

    // Blog category related translations
    'blog_category'                        => 'Blog Category',
    'blog_categories'                      => 'Blog Categories',
    'add_blog_category'                    => 'Add Blog Category',
    'edit_blog_category'                   => 'Edit Blog Category',
    'show_blog_category'                   => 'Show Blog Category',
    'delete_blog_category'                 => 'Delete Blog Category',
    'order_status_history'                 => 'Order Status History',
    'changed_by'                           => 'Changed By',
    'content_min'                          => 'The content must be at least 30 characters.',
    'content_required'                     => 'The content field is required.',
    'notes'                                => 'Notes',
    'mark_payment_as_paid'                 => 'Mark Payment as Paid',
    'confirm_mark_payment_paid'            => 'Are you sure you want to mark this payment as paid?',
    'amount_paid'                          => 'Amount Paid',

    // SweetAlert messages
    'confirm_status_change'                => 'This will update the order status.',
    'are_you_sure_change_order_status_to' => 'Are you sure you want to change the order status to',
    'yes_change'                           => 'Yes, Change',
    'yes_update'                           => 'Yes, Update',
    'cancel'                               => 'Cancel',
    'updating'                             => 'Updating...',
    'please_wait'                          => 'Please wait while we process your request.',
    'success'                              => 'Success!',
    'status_updated_successfully'          => 'Order status has been updated successfully.',
    'order_status_updated_successfully'    => 'Order status has been updated successfully.',
    'error'                                => 'Error!',
    'error_occurred'                       => 'An error occurred',
    'ok'                                   => 'OK',
    'processing'                           => 'Processing...',
    'payment_will_be_marked_paid'          => 'This will mark the payment as paid and update the order status.',
    'yes_mark_paid'                        => 'Yes, Mark as Paid',
    'payment_marked_paid_successfully'     => 'Payment has been marked as paid successfully.',
    'transfer_will_be_verified'            => 'This will verify the bank transfer and process the order.',
    'yes_verify'                           => 'Yes, Verify',
    'verifying'                            => 'Verifying...',
    'transfer_verified_successfully'       => 'Bank transfer has been verified successfully.',
    'please_provide_reason'                => 'Please provide a reason for rejection:',
    'enter_rejection_reason'               => 'Enter the reason for rejecting this transfer...',
    'yes_reject'                           => 'Yes, Reject',
    'reason_required'                      => 'You must provide a reason for rejection.',
    'rejecting'                            => 'Rejecting...',
    'transfer_rejected_successfully'       => 'Bank transfer has been rejected successfully.',
    'total_amount'                         => 'Total Amount',
    'total_items'                          => 'Total Items',
    'bank_transfer_pending_verification'   => 'Bank Transfer Pending Verification',
    'verify'                               => 'Verify',
    'reject'                               => 'Reject',
    'provider_order' => 'Provider Orders',

    // Order status filters
    'order_status'                         => 'Order Status',
    'pending_payment'                      => 'Pending Payment',
    'processing'                           => 'Processing',
    'confirmed'                            => 'Confirmed',
    'completed'                            => 'Completed',
    'cancelled'                            => 'Cancelled',
    'pending_verification'                 => 'Pending Verification',
    'paid'                                 => 'Paid',
    'failed'                               => 'Failed',
    'refunded'                             => 'Refunded',
    'wallet'                               => 'Wallet',
    'bank_transfer'                        => 'Bank Transfer',
    'credit_card'                          => 'Credit Card',
    'knet'                                 => 'KNET',
    'provider_total'                       => 'Provider Total',
    'mark_completed'                       => 'Mark Completed',
    'confirm_provider_status_change'       => 'This will update the provider status for this order.',
    'provider_status_updated_successfully' => 'Provider status has been updated successfully.',

    // Blog related translations
    'blog'                                 => 'Blog',
    'blogs'                                => 'Blogs',
    'add_blog'                             => 'Add Blog',
    'edit_blog'                            => 'Edit Blog',
    'view_blog'                            => 'View Blog',
    'delete_blog'                          => 'Delete Blog',
    'delete_group_of_blogs'                => 'Delete Multiple Blogs',
    'blog_title'                           => 'Blog Title',
    'blog_content'                         => 'Blog Content',
    'blog_category'                        => 'Blog Category',
    'blog_image'                           => 'Blog Image',
    'likes'                                => 'Likes',
    'dislikes'                             => 'Dislikes',
    'comments'                             => 'Comments',
    'no_comments_yet'                      => 'No comments yet',
    'approved'                             => 'Approved',
    'pending'                              => 'Pending',
    'comment_approved_successfully'        => 'Comment approved successfully',
    'comment_unapproved_successfully'      => 'Comment unapproved successfully',
    'error_occurred'                       => 'An error occurred',
    'toggle_comment_approval'              => 'Toggle Comment Approval',
    'approve_comment'                      => 'Approve Comment',
    'unapprove_comment'                    => 'Unapprove Comment',
    'load_more'                            => 'Load More',
    'loading'                              => 'Loading',
    'no_more_comments'                     => 'No more comments to load',
    'view_gift'                            => 'View Gift',
    'delete_gift'                          => 'Delete Gift',
    'orders_count'                         => 'Orders Count',
    'month'                                => 'Month',
    'coupon'                               => 'Coupon',
    'coupons'                              => 'Coupons',
    'coupons_count'                        => 'Coupons Count',
    'coupon_num'                           => 'Coupon Number',
    'max_discount'                         => 'Max Discount',
    'no_coupons_assigned'                  => 'No coupons assigned to this gift',
    'choose'                               => 'Choose...',
    'created_at'                           => 'Created At',
    'updated_at'                           => 'Updated At',
    'searching'                            => 'Searching',
    'no_results_found'                     => 'No results found',
    'please_enter_more_characters'         => 'Please enter more characters',
    'update_paymentmethod_page'            => 'Update Payment Method Page',
    'update_paymentmethod'                 => 'Update Payment Method',
    'show_paymentmethod_page'              => 'Show Payment Method Page',
    'delete_paymentmethod'                 => 'Delete Payment Method',
    'delete_group_of_paymentmethods'       => 'Delete Group of Payment Methods',
       'order_num' => 'Order Number',
    'type' => 'Type',
    'user' => 'User',
    'delivery' => 'Delivery Person',
    'address' => 'Address',
    'coupon_num' => 'Coupon Number',
    'coupon_type' => 'Coupon Type',
    'coupon_value' => 'Coupon Value',
    'total_products' => 'Total Products Price',
    'coupon_amount' => 'Coupon Discount Amount',
    'deliver_price' => 'Delivery Cost',
    'final_total' => 'Final Total',
    'status' => 'Order Status',
    'pay_status' => 'Payment Status',
    'pay_data' => 'Payment Data',
    'lat' => 'Latitude',
    'lng' => 'Longitude',
    'map_desc' => 'Map Description',
    'notes' => 'Notes',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'control' => 'Control',
    'show' => 'Show',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'paid' => 'Paid',
    'unpaid' => 'Unpaid',
    'loading' => 'Loading',
    'there_are_no_matches_matching' => 'No matching results found',
    'order_details' => 'order_details',
    'coupon' => 'coupon' ,
    'delivery_price' => 'Delivery Price',
    'map_description' => 'Map Description',
    'payment_method' => 'Payment Method',
    'City' => 'City',
    'select_city' => 'Select City',
    'addresses' => 'Addresses',
    'no_addresses_found' => 'No Addresses Found',
    'is_default' => 'Is Default',
    'yes' => 'Yes',
    'no' => 'No',
    'notifications' => 'Notifications',
    'no_notifications_found' => 'No Notifications Found',
    'no_message' => 'No Message',
    'read_status' => 'Read Status',
    'read' => 'Read',
    'unread' => 'Unread',
    'message' => 'Message',
    'order_number' => 'Order Number',
    'no_provider' => 'No Provider',
    'sar' => 'SAR',
    'unknown' => 'Unknown',
    'paid' => 'Paid',
    'pending' => 'Pending',
    'failed' => 'Failed',
    'refunded' => 'Refunded',
    'pending_payment' => 'Pending Payment',
    'processing' => 'Processing',
    'confirmed' => 'Confirmed',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'request_cancel' => 'Cancel Request',
    'course_name' => 'Course Name',
    'instructor' => 'Instructor',
    'enrollment_status' => 'Enrollment Status',
    'enrollment_active' => 'Active',
    'enrollment_completed' => 'Completed',
    'enrollment_pending_payment' => 'Pending Payment',
    'enrollment_cancelled' => 'Cancelled',
    'payment_paid' => 'Paid',
    'payment_pending' => 'Pending',
    'payment_failed' => 'Failed',
    'payment_refunded' => 'Refunded',
    'progress_percentage' => 'Progress',
    'enrollment_date' => 'Enrollment Date',
    'no_courses_found' => 'No Courses Found',




    // VAT related translations
    'vat_amount'                           => 'VAT Amount',
    'vat_amount_help'                      => 'Enter the VAT percentage to be applied to orders (e.g., 15 for 15%)',

    // Course related translations
    'course_name'                          => 'Course Name',
    'instructor'                           => 'Instructor',
    'instructor_name'                      => 'Instructor Name',
    'duration_hours'                       => 'Duration (Hours)',
    'stages_count'                         => 'Stages Count',
    'stage'                                => 'Stage',
    'price_from'                           => 'Price From',
    'price_to'                             => 'Price To',
    'all'                                  => 'All',
    'no_courses_found'                     => 'No courses found',
    'course_stages'                        => 'Course Stages',
    'add_stage'                            => 'Add Stage',
    'add_course'                           => 'Add Course',
    'save'                                 => 'Save',
    'cancel'                               => 'Cancel',

    // Course route titles
    'courses'                              => 'Courses',
    'add_course_page'                      => 'Add Course Page',
    'edit_course_page'                     => 'Edit Course Page',
    'edit_course'                          => 'Edit Course',
    'show_course_page'                     => 'Show Course Page',
    'delete_course'                        => 'Delete Course',
    'delete_multible_course'               => 'Delete Multiple Courses',
    'toggle_course_status'                 => 'Toggle Course Status',

    // Product Categories related translations
    'product-categories'                   => 'Product Categories',
    'product_category'                     => 'Product Category',
    'add_product_category_page'            => 'Add Product Category Page',
    'add_product_category'                 => 'Add Product Category',
    'update_product_category_page'         => 'Update Product Category Page',
    'update_product_category'              => 'Update Product Category',
    'show_product_category_page'           => 'Show Product Category Page',
    'delete_product_category'              => 'Delete Product Category',
    'delete_group_of_product_categories'   => 'Delete Group of Product Categories',
    'category_name'                        => 'Category Name',
    'category_description'                 => 'Category Description',
    'category_image'                       => 'Category Image',
    'parent_category'                      => 'Parent Category',
    'subcategories'                        => 'Subcategories',
    'products_count'                       => 'Products Count',
    'category_status'                      => 'Category Status',

    // Course related translations
    'courses'                              => 'Courses',
    'course'                               => 'Course',
    'course_details'                       => 'Course Details',
    'course_name_ar'                       => 'Course Name (Arabic)',
    'course_name_en'                       => 'Course Name (English)',
    'instructor_name_ar'                   => 'Instructor Name (Arabic)',
    'instructor_name_en'                   => 'Instructor Name (English)',
    'course_duration'                      => 'Course Duration',
    'course_price'                         => 'Course Price',
    'course_description'                   => 'Course Description',
    'course_description_ar'                => 'Description in Arabic',

    // Course Enrollment related translations
    'course_enrollments'                   => 'Course Enrollments',
    'course_enrollment'                    => 'Course Enrollment',
    'enrollment_id'                        => 'Enrollment ID',
    'enrollment_date'                      => 'Enrollment Date',
    'enrollment_time'                      => 'Enrollment Time',
    'enrollment_datetime'                  => 'Enrollment Date & Time',
    'course_provider'                      => 'Course Provider',
    'client_name'                          => 'Client Name',
    'mobile_number'                        => 'Mobile Number',
    'amount_paid'                          => 'Amount Paid',
    'payment_method'                       => 'Payment Method',
    'payment_reference'                    => 'Payment Reference',
    'download_invoice'                     => 'Download Invoice',
    'download_pdf'                         => 'Download PDF',
    'serial_number'                        => 'Serial',
    'course_enrollments_management'        => 'Course Enrollments Management',
    'view_course_enrollments'              => 'View Course Enrollments',
    'export_enrollments'                   => 'Export Enrollments',
    'download_enrollment_pdf'              => 'Download Enrollment PDF',
    'enrollment_details'                   => 'Enrollment Details',
    'no_data_found'                        => 'No Data Found',
    'course_description_en'                => 'Description in English',
    'course_stages'                        => 'Course Stages',
    'course_stages_count'                  => 'Number of Stages',
    'course_image'                         => 'Course Image',
    'course_status'                        => 'Course Status',
    'course_active'                        => 'Active',
    'course_inactive'                      => 'Inactive',
    'creation_date'                        => 'Creation Date',
    'hours'                                => 'Hours',
    'riyal'                                => 'SAR',
    'stage'                                => 'Stage',
    'stages'                               => 'Stages',
    'stage_number'                         => 'Stage',
    'stage_title_ar'                       => 'Title (Arabic)',
    'stage_title_en'                       => 'Title (English)',
    'stage_video'                          => 'Video',
    'watch_video'                          => 'Watch Video',
    'no_stages_yet'                        => 'No stages for this course yet',

    // Course Enrollments
    'course_enrollments'                   => 'Course Enrollments',
    'total_enrollments'                    => 'Total Enrollments',
    'active_enrollments'                   => 'Active Students',
    'completed_enrollments'                => 'Completed',
    'pending_payments'                     => 'Pending Payment',
    'student_name'                         => 'Student Name',
    'student_email'                        => 'Email',
    'enrollment_date'                      => 'Enrollment Date',
    'enrollment_status'                    => 'Enrollment Status',
    'payment_status'                       => 'Payment Status',
    'payment_method'                       => 'Payment Method',
    'amount_paid'                          => 'Amount Paid',
    'progress_percentage'                  => 'Progress',
    'completion_date'                      => 'Completion Date',
    'actions'                              => 'Actions',
    'no_enrollments_yet'                   => 'No enrollments for this course yet',

    // Enrollment Status
    'enrollment_active'                    => 'Active',
    'enrollment_completed'                 => 'Completed',
    'enrollment_pending_payment'           => 'Pending Payment',
    'enrollment_cancelled'                 => 'Cancelled',
    'enrollment_failed'                    => 'Failed',

    // Payment Status
    'payment_paid'                         => 'Paid',
    'payment_pending'                      => 'Pending',
    'payment_failed'                       => 'Failed',
    'payment_refunded'                     => 'Refunded',

    // Payment Methods
    'payment_wallet'                       => 'Wallet',
    'payment_credit_card'                  => 'Credit Card',
    'payment_mada'                         => 'Mada',
    'payment_apple_pay'                    => 'Apple Pay',
    'payment_bank_transfer'                => 'Bank Transfer',

    // Actions
    'confirm_payment'                      => 'Confirm Payment',
    'mark_completed'                       => 'Graduate',
    'graduate_student'                     => 'Graduate Student',
    'view_user'                            => 'View User',
    'not_completed'                        => 'Not Completed',

    // Modal Messages
    'confirm_payment_title'                => 'Confirm Payment',
    'confirm_payment_message'              => 'Are you sure you want to confirm payment for this enrollment?',
    'mark_completed_title'                 => 'Graduate Student',
    'mark_completed_message'               => 'Are you sure you want to graduate this student and complete the course?',
    'payment_confirmed_success'            => 'Payment confirmed successfully',
    'student_graduated_success'            => 'Student graduated successfully',
    'payment_confirmation_error'           => 'Error occurred while confirming payment',
    'graduation_error'                     => 'Error occurred while graduating student',

    // Navigation
    'edit_course'                          => 'Edit',
    'back_to_courses'                      => 'Back',
    'cancel'                               => 'Cancel',

    // Blog Categories
    'blogcategories'                       => 'Blog Categories',
    'add_blogcategory_page'                => 'Add Blog Category Page',
    'add_blogcategory'                     => 'Add Blog Category',
    'update_blogcategory_page'             => 'Update Blog Category Page',
    'update_blogcategory'                  => 'Update Blog Category',
    'show_blogcategory_page'               => 'Show Blog Category Page',
    'delete_blogcategory'                  => 'Delete Blog Category',
    'delete_group_of_blogcategories'       => 'Delete Group of Blog Categories',

    // Blogs
    'blogs'                                => 'Blogs',
    'blog'                                 => 'Blog',
    'add_blog_page'                        => 'Add Blog Page',
    'add_blog'                             => 'Add Blog',
    'update_blog_page'                     => 'Update Blog Page',
    'update_blog'                          => 'Update Blog',
    'show_blog_page'                       => 'Show Blog Page',
    'delete_blog'                          => 'Delete Blog',
    'delete_group_of_blogs'                => 'Delete Group of Blogs',
    'blog_title'                           => 'Blog Title',
    'blog_content'                         => 'Blog Content',
    'blog_category'                        => 'Blog Category',
    'blog_image'                           => 'Blog Image',
    'select_category'                      => 'Select Category',
    'content'                              => 'Content',

    // Coupon related translations
    'coupon_name'                          => 'Coupon Name',
    'enter_coupon_name'                    => 'Enter Coupon Name',
    'select_provider'                      => 'Select Provider',
    'provider'                             => 'Provider',
    'provider_not_found'                   => 'Provider Not Found',
    'global_coupon'                        => 'Global Coupon',
    'zero_means_unlimited'                 => 'Zero means unlimited',
    'used_times'                           => 'Used Times',
    'usage'                                => 'Usage',
    'unlimited'                            => 'Unlimited',
    'max'                                  => 'Max',
    'percentage'                           => 'Percentage',
    'not_set'                              => 'Not Set',
    'no_expiry'                            => 'No Expiry',
    'leave_empty_for_no_expiry'            => 'Leave empty for no expiry',

    // Provider management translations
    'providers'                           => 'Providers',
    'pending_provider_requests'           => 'Pending Provider Requests',
    'add_provider'                        => 'Add Provider',
    'edit_provider'                       => 'Edit Provider',
    'view_provider'                       => 'View Provider',
    'block_provider'                      => 'Block Provider',
    'delete_provider'                     => 'Delete Provider',
    'delete_multiple_providers'           => 'Delete Multiple Providers',
    'notify_providers'                    => 'Notify Providers',
    'update_provider_balance'             => 'Update Provider Balance',
    'approve_provider_request'            => 'Approve Provider Request',
    'reject_provider_request'             => 'Reject Provider Request',
    'provider_details'                    => 'Provider Details',
    'commercial_register_no'              => 'Commercial Register Number',
    'institution_name'                    => 'Institution Name',
    'sponsor_name'                        => 'Sponsor Name',
    'sponsor_phone'                       => 'Sponsor Phone',
    'salon_type'                          => 'Salon Type',
    'is_mobile'                           => 'Mobile Service',
    'mobile_service_fee'                  => 'Mobile Service Fee',
    'nationality'                         => 'Nationality',
    'residence_type'                      => 'Residence Type',
    'in_home'                             => 'Home Service',
    'in_salon'                            => 'Salon Service',
    'home_fees'                           => 'Home Service Fees',
    'provider_status'                     => 'Provider Status',
    'approve_request'                     => 'Approve Request',
    'reject_request'                      => 'Reject Request',
    'provider_approved_successfully'      => 'Provider approved successfully',
    'provider_rejected_successfully'      => 'Provider rejected successfully',
    'all_providers'                       => 'All Providers',
    'new_provider_requests'               => 'New Provider Requests',
    'no_pending_requests'                 => 'No Pending Requests',
    'no_pending_provider_requests_found'  => 'No pending provider requests found',
    'back_to_all_providers'               => 'Back to All Providers',
    'unblock'                             => 'Unblock',
    'block'                               => 'Block',
    'enter_rejection_reason'              => 'Enter rejection reason...',

    // Provider form translations
    'basic_information'                   => 'Basic Information',
    'provider_information'                => 'Provider Information',
    'documents'                           => 'Documents',
    'profile_image'                       => 'Profile Image',
    'select_status'                       => 'Select Status',
    'select_residence_type'               => 'Select Residence Type',
    'select_salon_type'                   => 'Select Salon Type',
    'citizen'                             => 'Citizen',
    'resident'                            => 'Resident',
    'visitor'                             => 'Visitor',
    'men'                                 => 'Men',
    'women'                               => 'Women',
    'both'                                => 'Both',
    'provides_home_service'               => 'Provides Home Service',
    'provides_salon_service'              => 'Provides Salon Service',
    'logo'                                => 'Logo',
    'commercial_register_image'           => 'Commercial Register Image',
    'residence_image'                     => 'Residence Image',
    'leave_empty_to_keep_current'         => 'Leave empty to keep current password',

    // Validation messages
    'max_length_191'                      => 'Maximum length is 191 characters',
    'max_length_50'                       => 'Maximum length is 50 characters',
    'max_length_20'                       => 'Maximum length is 20 characters',
    'max_length_100'                      => 'Maximum length is 100 characters',
    'max_length_1000'                     => 'Maximum length is 1000 characters',
    'must_be_numeric'                     => 'This field must be numeric',
    'country_code_digits_between_2_5'     => 'Country code must be between 2 and 5 digits',
    'phone_min_8_digits'                  => 'Phone number must be at least 8 digits',
    'phone_already_exists'                => 'This phone number already exists',
    'email_already_exists'                => 'This email address already exists',
    'password_min_6_characters'           => 'Password must be at least 6 characters',
    'city_not_found'                      => 'Selected city not found',
    'invalid_status'                      => 'Invalid status selected',
    'must_be_image'                       => 'File must be an image',
    'image_max_size_2mb'                  => 'Image size must not exceed 2MB',
    'must_be_positive'                    => 'Value must be positive',
    'max_amount_999999'                   => 'Maximum amount is 999,999',
    'invalid_residence_type'              => 'Invalid residence type',
    'invalid_salon_type'                  => 'Invalid salon type',
    'invalid_gender'                      => 'Invalid gender selected',
    'invalid_nationality'                 => 'Invalid nationality selected',
    'password_confirmation_does_not_match' => 'Password confirmation does not match',
    'min_length_10'                       => 'Minimum length is 10 characters',
    'id_number_must_be_10_digits'         => 'ID number must be exactly 10 digits',
    'must_be_text'                        => 'This field must be text',
    'invalid_format'                      => 'Invalid format',
    'validation_error'                    => 'Validation Error',
    'gender'                              => 'Gender',
    'select_gender'                       => 'Select Gender',
    'male'                                => 'Male',
    'female'                              => 'Female',
    'arabic'                              => 'Arabic',
    'english'                             => 'English',
    'in_arabic'                           => 'in Arabic',
    'in_english'                          => 'in English',
    'select_nationality'                  => 'Select Nationality',
    'saudi'                               => 'Saudi',
    'other'                               => 'Other',
    'individual'                          => 'Individual',
    'professional'                        => 'Professional',
    'salon'                               => 'Salon',
    'beauty_center'                       => 'Beauty Center',
    'latitude'                            => 'Latitude',
    'longitude'                           => 'Longitude',
    'required_if_nationality_other'       => 'Required if nationality is other',
    'block_provider'                      => 'Block this provider?',
    'unblock_provider'                    => 'Unblock this provider?',
    'delete_provider_warning'             => 'This action cannot be undone!',
    'provider_deleted_successfully'       => 'Provider deleted successfully',
    'operation_completed_successfully'    => 'Operation completed successfully',
    'client_activated'                    => 'Client activated successfully',
    'client_deactivated'                  => 'Client deactivated successfully',
    'pending'                             => 'Pending',
    'rejected'                            => 'Rejected',
    'status_updated_successfully'         => 'Status updated successfully',
    'approved'                            => 'Approved',
    'blocked'                             => 'Blocked',
    'active'                              => 'Active',
    'in_review'                           => 'In Review',
    'accepted'                            => 'Accepted',
    'deleted'                             => 'Deleted',
    'provider_status'                     => 'Provider Status',
    'approved_active'                     => 'Approved & Active',
    'phone_status'                        => 'Phone Status',
    'inactive'                            => 'Inactive',
    'edit_provider'                       => 'Edit Provider',
    'basic_information'                   => 'Basic Information',
    'provider_information'                => 'Provider Information',
    'documents'                           => 'Documents',
    'request_status'                      => 'Request Status',
    'all_statuses'                        => 'All Statuses',
    'already_rejected'                    => 'Already Rejected',
    'view_rejection_reason'               => 'View Rejection Reason',
    'phone_rejected_cannot_reregister'    => 'This phone number is rejected and cannot be re-registered',
    'provider_approved_successfully'      => 'Provider approved successfully',
    'provider_rejected_successfully'      => 'Provider rejected successfully',
    'provider_not_found'                  => 'Provider not found',
    'approve_provider_request'            => 'Do you want to approve this provider request?',
    'approve_request'                     => 'Approve Request',
    'reject_request'                      => 'Reject Request',
    'cancel'                              => 'Cancel',
    'are_you_sure'                        => 'Are you sure?',
    'error_occurred'                      => 'An error occurred',
    'success'                             => 'Success',
    'error'                               => 'Error',
    'rejection_reason'                    => 'Rejection Reason',
    'enter_rejection_reason'              => 'Enter rejection reason',
    'reason_required'                     => 'Rejection reason is required',
    'back_to_all_providers'               => 'Back to All Providers',
    'provider_details'                    => 'Provider Details',
    'no_provider_information'             => 'No provider information available',
    'no_image_uploaded'                   => 'No image uploaded',
    'salon_images'                        => 'Salon Images',
    'salon_image'                         => 'Salon Image',
    'no_services_found'                   => 'No services found',
    'no_orders_found'                     => 'No orders found',
    'no_ratings_found'                    => 'No ratings found',
    'based_on'                            => 'Based on',
    'reviews'                             => 'reviews',
    'recent_reviews'                      => 'Recent Reviews',
    'view_all_orders'                     => 'View All Orders',
    'order_number'                        => 'Order Number',
    'client'                              => 'Client',
    'total_amount'                        => 'Total Amount',
    'payment_method'                      => 'Payment Method',
    'actions'                             => 'Actions',
    'confirmed'                           => 'Confirmed',
    'completed'                           => 'Completed',
    'cancelled'                           => 'Cancelled',
    'minutes'                             => 'minutes',
    'currency'                            => 'SAR',
    'yes'                                 => 'Yes',
    'no'                                  => 'No',
    'inactive'                            => 'Inactive',
    'back'                                => 'Back',
    'products'                            => 'Products',
    'working_hours'                       => 'Working Hours',
    'stock_quantity'                      => 'Stock Quantity',
    'out_of_stock'                        => 'Out of Stock',
    'no_products_found'                   => 'No products found',
    'no_working_hours_found'              => 'No working hours set',
    'day'                                 => 'Day',
    'start_time'                          => 'Start Time',
    'end_time'                            => 'End Time',
    'is_working'                          => 'Is Working',
    'open'                                => 'Open',
    'closed'                              => 'Closed',
    'sunday'                              => 'Sunday',
    'monday'                              => 'Monday',
    'tuesday'                             => 'Tuesday',
    'wednesday'                           => 'Wednesday',
    'thursday'                            => 'Thursday',
    'friday'                              => 'Friday',
    'saturday'                            => 'Saturday',
    'special_dates'                       => 'Special Dates',
    'date'                                => 'Date',
    'working_hours_must_be_array'         => 'Working hours must be an array',
    'day_is_required'                     => 'Day is required',
    'invalid_day'                         => 'Invalid day selected',
    'start_time_is_required'              => 'Start time is required',
    'end_time_is_required'                => 'End time is required',
    'invalid_time_format'                 => 'Invalid time format (use HH:MM)',
    'end_time_must_be_after_start_time'   => 'End time must be after start time',
    'please_fill_working_hours'           => 'Please fill in start and end times for all working days',
    'at_least_one_working_day_required'   => 'At least one working day is required',
    'start_time_required_for_day'         => 'Start time is required for :day',
    'end_time_required_for_day'           => 'End time is required for :day',
    'rate_type'                           => 'Rate Type',
    'rateable_name'                       => 'Rated Item',
    'user_name'                           => 'User Name',
    'review'                              => 'Review',
    'provider'                            => 'Provider',
    'product'                             => 'Product',
    'service'                             => 'Service',
    'no_review'                           => 'No review provided',
    'view_full'                           => 'View Full',
    'videos'                              => 'Videos',
    'images'                              => 'Images',
    'publish_video'                       => 'Publish Video',
    'publish_video_confirmation'          => 'Are you sure you want to publish this video as a short video?',
    'yes_publish'                         => 'Yes, Publish',
    'published'                           => 'Published',
    'video_published_successfully'        => 'Video published successfully',
    'change_status_confirmation'          => 'Are you sure you want to change the status?',
    'yes_change'                          => 'Yes, Change',
    'status_updated_successfully'         => 'Status updated successfully',
    'invalid_status_provided'             => 'Invalid status provided',
    'video_not_found_for_rate'            => 'Video not found for this rate',
    'selected_media_not_video'            => 'Selected media is not a video file',
    'video_already_published'             => 'Video already published',
    'rate_details'                        => 'Rate Details',
    'rated_item_information'              => 'Rated Item Information',
    'customer_information'                => 'Customer Information',
    'customer_name'                       => 'Customer Name',
    'review_date'                         => 'Review Date',
    'rating'                              => 'Rating',
    'overall_rating'                      => 'Overall Rating',
    'review_body'                         => 'Review Body',
    'rate_image'                          => 'Rate Image',
    'status_management'                   => 'Status Management',
    'current_status'                      => 'Current Status',
    'status_change_note'                  => 'Changing the status will notify the customer.',
    'quick_stats'                         => 'Quick Stats',
    'rating_value'                        => 'Rating Value',
    'images_count'                        => 'Images Count',
    'videos_count'                        => 'Videos Count',
    'image_preview'                       => 'Image Preview',
    'confirm_status_change'               => 'Confirm Status Change',
    'status_change_confirmation'          => 'Are you sure you want to change the status of this rate?',
    'confirm_publish_title'               => 'Publish Video',
    'confirm_publish_text'                => 'Are you sure you want to publish this video as a short video?',
    'success'                             => 'Success',
    'error'                               => 'Error',
    'are_you_sure_change_status'          => 'Are you sure you want to change the status?',
    'updating'                            => 'Updating...',
    'please_wait'                         => 'Please wait while we update the status.',
    'ok'                                  => 'OK',
    'reason'                              => 'Reason',
    'account_deletion_requests'           => 'Account Deletion Requests',
    'account_deletion_request_details'    => 'Account Deletion Request Details',
    'user_reason'                         => 'User Reason',
    'admin_notes'                         => 'Admin Notes',
    'approve_deletion_request'            => 'Approve Deletion Request',
    'reject_deletion_request'             => 'Reject Deletion Request',
    'approve_deletion_warning'            => 'Warning: Approving this request will permanently delete the account.',
    'enter_admin_notes'                   => 'Enter admin notes',
    'enter_rejection_reason'              => 'Enter rejection reason',
    'rejection_reason_required'           => 'Rejection reason is required',
    'deletion_request_approved_successfully' => 'Deletion request approved successfully',
    'deletion_request_rejected_successfully' => 'Deletion request rejected successfully',
    'no_deletion_requests'                => 'No Deletion Requests',
    'no_deletion_requests_found'          => 'No account deletion requests found',
    'request_date'                        => 'Request Date',
    'processed_date'                      => 'Processed Date',
    'not_processed'                       => 'Not Processed',
    'already_processed'                   => 'Already Processed',
    'no_reason_provided'                  => 'No reason provided',
    'user_information'                    => 'User Information',
    'request_information'                 => 'Request Information',
    'registration_date'                   => 'Registration Date',
    'deleted_user'                        => 'Deleted User',
    'close'                               => 'Close',

    // Order Rate related translations
    'orderrate'                           => 'Order Rate',
    'orderrates'                          => 'Order Rates',
    'order_rate_details'                  => 'Order Rate Details',
    'timing_rate'                         => 'Timing Rate',
    'quality_rate'                        => 'Quality Rate',
    'service_rate'                        => 'Service Rate',
    'average_rating'                      => 'Average Rating',
    'review_body'                         => 'Review Body',
    'order_information'                   => 'Order Information',
    'customer_information'                => 'Customer Information',
    'customer_name'                       => 'Customer Name',
    'order_number'                        => 'Order Number',
    'order_date'                          => 'Order Date',
    'review_date'                         => 'Review Date',
    'ratings'                             => 'Ratings',
    'images'                              => 'Images',
    'order_rate_image'                    => 'Order Rate Image',
    'no_images_uploaded'                  => 'No images uploaded',
    'status_management'                   => 'Status Management',
    'current_status'                      => 'Current Status',
    'pending'                             => 'Pending',
    'approved'                            => 'Approved',
    'rejected'                            => 'Rejected',
    'status_change_note'                  => 'Changes will be saved automatically',
    'quick_stats'                         => 'Quick Stats',
    'average'                             => 'Average',
    'actions'                             => 'Actions',
    'back_to_list'                        => 'Back to List',
    'view_order'                          => 'View Order',
    'image_preview'                       => 'Image Preview',
    'created_at'                          => 'Created At',
    'updated_at'                          => 'Updated At',
    'add_orderrate_page'                  => 'Add Order Rate Page',
    'add_orderrate'                       => 'Add Order Rate',
    'update_orderrate_page'               => 'Update Order Rate Page',
    'update_orderrate'                    => 'Update Order Rate',
    'show_orderrate_page'                 => 'Show Order Rate Page',
    'delete_orderrate'                    => 'Delete Order Rate',
    'delete_group_of_orderrates'          => 'Delete Group of Order Rates',
    'update_orderrate_status'             => 'Update Order Rate Status',
    'ok'                                  => 'OK',
    'please_wait'                         => 'Please wait...',
    'updating'                            => 'Updating...',
    'status_updated_successfully'         => 'Status updated successfully',
    'confirm_status_change'               => 'Confirm Status Change',
    'are_you_sure_change_status'          => 'Are you sure you want to change the status?',
    'yes_change'                          => 'Yes, Change',
    'number'                              => 'Number',
    'videos' => 'videos' ,
    'publish_video' =>  'Publish Video',
    'already_published' => 'Already Published',
    'published_at' => 'Published At',
    'confirm_publish_title' => 'Confirm',
    'confirm_publish_text' => 'Do you want to publish this video?',
    'yes_publish' => 'Yes, publish it',
    'cancel' => 'Cancel',
    'success' => 'Success',
    'error' => 'Error',
    'error_occurred' => 'Something went wrong.',
    'shortvideos'                     => 'Short Videos',
    'show_shortvideo_page'           => 'Show Short Video Page',
    'delete_shortvideo'              => 'Delete Short Video',
    'delete_group_of_shortvideos'    => 'Delete Group of Short Videos',
    'video_id'         => 'Video UUID',
'order_rate_id'    => 'Order Rate ID',
'visa' => 'Visa',
'mada' => 'Mada',
'apple_pay' => 'Apple Pay',
'google_pay' => 'Google Pay',
'bank_transfer' => 'Bank Transfer',
'wallet' => 'Wallet',
'Loyality_points-reports' => 'Loyality Points Reports',
'loyalty_points_settings' => 'Loyalty Points Settings',
    'loyalty_points_description' => 'Control how loyalty points work in the app',
    'enable_loyalty_points' => 'Enable Loyalty Points System',
    'points_earned_per_sar' => 'Points Earned per SAR',
    'points_earn_example' => 'Example: 1 = One point per SAR paid',
    'point_value_in_sar' => 'Value of One Point in SAR',
    'point_value_example' => 'Example: 1 = One point equals 1 SAR',
    'min_points_to_redeem' => 'Minimum Points to Redeem',
    'min_points_hint' => 'The minimum number of points that can be used for payment',
    'max_points_percentage' => 'Maximum Points Usage (%)',
    'max_points_hint' => 'Maximum percentage of order value that can be paid with points',
    'loyalty_notes_title' => 'Important Notes:',
    'note_points_on_payment' => 'Points are only earned on actual payments (online, bank transfer, or wallet balance)',
    'note_used_points_no_earn' => 'Used points do not earn additional points',
    'note_earned_from_paid_amount' => 'Earned points are calculated based on the actually paid amount',
    'saving_changes' => 'Save Changes',
    'back' => 'Back',
    "platform_commission_settings" => "Platform Commission Settings",
    "referral_commission_settings" => "Referral Commission Settings",
    'loyalty_points' => 'Loyalty Points',
    'current_loyalty_points' => 'Current Loyalty Points',
    'orders_with_loyalty_points_used' => 'Orders with Loyalty Points Used',
    'order_number' => 'Order Number',
    'total' => 'Total',
    'loyalty_points_used' => 'Loyalty Points Used',
    'favorites' => 'Favorites',
    'item_type' => 'Item Type',
    'item_name' => 'Item Name',
    'date_added' => 'Date Added',
    'product' => 'Product',
    'service' => 'Service',
    'provider' => 'Provider',
    'unknown' => 'Unknown',
    'item_not_found' => 'Item Not Found',
    'item_deleted' => 'Item Deleted',
    'no_favorites_found' => 'No favorites found',
    'payments' => 'Payments',
    'transaction_id' => 'Transaction ID',
    'amount' => 'Amount',
    'transaction_type' => 'Type',
    'transaction_status' => 'Status',
    'transaction_message' => 'Message',
    'date' => 'Date',
    'order_payment' => 'Order Payment',
    'course_payment' => 'Course Payment',
    'wallet_recharge' => 'Wallet Recharge',
    'refund' => 'Refund',
    'completed' => 'Completed',
    'pending' => 'Pending',
    'failed' => 'Failed',
    'cancelled' => 'Cancelled',
    'no_payments_found' => 'No payments found',

    // Order Statuses
    'pending_payment' => 'Pending Payment',
    'processing' => 'Processing',
    'confirmed' => 'Confirmed',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'pending_verification' => 'Pending Verification',
    'request_cancel' => 'Cancel Requested',
    'new' => 'New',

    // Payment Statuses
    'paid' => 'Paid',
    'pending' => 'Pending',
    'failed' => 'Failed',
    'refunded' => 'Refunded',
    'pending_verification' => 'Pending Verification',

    // Booking Types
    'home' => 'Home Service',
    'salon' => 'Salon Service',

    // Delivery Types
    'normal' => 'Normal Delivery',
    'express' => 'Express Delivery',

    // Transaction Types
    'pay-order' => 'Order Payment',
    'pay-course' => 'Course Payment',
    'wallet-recharge' => 'Wallet Recharge',
    'refund' => 'Refund',
    'loyalty-points-earned' => 'Loyalty Points Earned',
    'loyalty-points-used' => 'Loyalty Points Used',

    // Transaction Statuses
    'success' => 'Success',
    'pending' => 'Pending',
    'failed' => 'Failed',
    'cancelled' => 'Cancelled',
    'processing' => 'Processing',
    'completed' => 'Completed',

    // Bank Transfer Statuses
    'verified' => 'Verified',
    'rejected' => 'Rejected',

    // General
    'unknown' => 'Unknown',

    // Order Rating
    'order_rating' => 'Order Rating',
    'rating_breakdown' => 'Rating Breakdown',
    'review_details' => 'Review Details',
    'rating_status' => 'Rating Status',
    'rating_media' => 'Rating Media',
    'video_not_supported' => 'Video not supported',
    'no_rating_yet' => 'No Rating Yet',
    'customer_has_not_rated_this_order' => 'The customer has not rated this order yet',
'cant_delete_provider' => 'cant delete provider , have orders',
    // Media Picker
    'drag_drop_or_click_to_upload' => 'Drag & drop files here or click to browse',
    'max_file_size' => 'Maximum file size',
    'uploading' => 'Uploading',
    'file_uploaded_successfully' => 'File uploaded successfully',
    'upload_failed' => 'Upload failed',
    'file_deleted_successfully' => 'File deleted successfully',
    'delete_failed' => 'Delete failed',
    'file_type_not_allowed' => 'File type not allowed',
    'file_too_large' => 'File too large',
    'points' => 'Points',
    'value' => 'Value',
    'blocked_users' => 'Blocked Users',
    'active_providers' => 'Active Providers',
    'inactive_providers' => 'Inactive Providers',
    'accepted_providers' => 'Accepted Providers',
    'rejected_providers' => 'Rejected Providers',
    'blocked_providers' => 'Blocked Providers',
    'id_num'      => 'id_num',

    'total_orders' => 'Total Orders',
    'orders_with_products' => 'Orders with Products',
    'orders_with_services' => 'Orders with Services',
    'orders_only_services' => 'Orders with Only Services',
    'orders_only_products' => 'Orders with Only Products',
    'orders_mixed' => 'Mixed Orders',
    'total_clients' => 'Total Clients',
    'total_client_deletion_requests' => 'Total Client Account Deletion Requests',
    'total_blocked_clients' => 'Total Blocked Clients',
    'total_service_orders' => 'Total Service Orders',
    'total_provider_registration_requests' => 'Total Provider Registration Requests',
    'total_provider_deletion_requests' => 'Total Provider Account Deletion Requests',
    'total_blocked_providers' => 'Total Blocked Providers',
    'total_active_providers' => 'Total Active Providers',
    'total_bookings' => 'Total Bookings',
    'platform_commission' => 'Platform Commission',
    'delivery_fees' => 'Delivery Fees',
    'cancel_fees' => 'Cancel Fees',
    'total_revenue' => 'Total Revenue',
    'total_discounts' => 'Total Discounts',
    'total_revenue' => 'Total Revenue',
    'revenue_reports' => 'Revenue Reports',
    'payment_reports' => 'Payment Reports',
    'withdraw_requests_reports' => 'Withdraw Requests Reports', 

    'total_bookings_pending_payment_confirmation' => 'Total Bookings Pending Payment Confirmation',
    'total_bookings_processing' => 'Total Bookings Under Processing',
    'total_bookings_ongoing' => 'Total Ongoing Bookings',
    'total_bookings_cancelled' => 'Total Cancelled Bookings',
    'total_bookings_completed' => 'Total Completed Bookings',
    'total_bookings_failed' => 'Total Failed Bookings',
    'total_consultation_requests' => 'Total Consultation Requests',
    'total_unread_contactus_messages' => 'Total Unread Contact Us Messages',
    'withdraw_request_accepted' => 'Withdraw request accepted',
    'withdraw_request_accepted_body' => 'Your withdraw request has been accepted.',
    'withdraw_request_rejected' => 'Withdraw request rejected',
    'withdraw_request_rejected_body' => 'Your withdraw request has been rejected.',
    'withdraw_request_accepted_admin' => 'Withdraw request accepted',
    'withdraw_request_accepted_admin_body' => 'Withdraw request from :provider for amount :amount has been accepted.',
    'withdraw_request_rejected_admin' => 'Withdraw request rejected',
    'withdraw_request_rejected_admin_body' => 'Withdraw request from :provider for amount :amount has been rejected.',
    'withdraw_request' => 'Withdraw Request',
    'booking_fees' => 'Booking Fee',
    "commission_reports" => "Comission Reports",
    'all_consultations' => 'All Consultations',
    'consultations' => 'Consultations',
    'show_consultation_conversation' => 'Show Consultation Conversation',
    'reply_to_consultation' => 'Reply to Consultation',
    'valid' => 'Valid',
    'expired' => 'Expired',
    'bank_account' => 'Bank Account',
    'cannot_update_cancelled_order' => 'Cannot update a cancelled order.',
    'status_already_in_history' => 'This status already exists in the order history.',
    'status_changed_to_by_admin' => 'Status changed to :status by admin',
    'client_location' => 'Client Location',
    'provider_location' => 'Provider Location',
    'select_provider' => 'Select Provider',
    'provider_name' => 'Provider Name',
    'coordinates' => 'Coordinates',
    'user_location' => 'User Location',
    'delivery_address' => 'Delivery Address',
    'debug_info' => 'Debug Info',
    'filter_by_provider' => 'Filter by Provider',
    'show_all_providers' => 'Show All Providers',
    'location_maps' => 'Location Maps',
    'quick_stats' => 'Quick Statistics',
    'expected_time_to_accept' => 'Expected Time to Accept',
    'minutes' => 'minutes',
    'cancel_request_accepted_map_desc' => 'Cancel request accepted. Refund: :refund_amount, Fees: :cancel_fees',

    // Multi-step form validation messages
    'validation_errors_found' => 'Validation Errors Found',
    'please_check_the_following_tabs_for_errors' => 'Please check the following tabs for errors',
    'errors' => 'errors',
    'errors_found' => 'Errors Found',
    'validation_errors' => 'Validation Errors',
    'please_fix_validation_errors_before_submitting' => 'Please fix validation errors before submitting the form',
    'please_check_errors_in_tabs' => 'Please check errors in the following tabs',
    'submitting' => 'Submitting',
    'please_fix_the_following_errors' => 'Please fix the following errors',
    'ok' => 'OK',
];