<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\blogcategories\Store;
use App\Http\Requests\Admin\blogcategories\Update;
use App\Models\BlogCategory ;
use App\Traits\Report;


class BlogCategoryController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {
            $blogcategories = BlogCategory::search(request()->searchArray)->paginate(30);
            $html = view('admin.blogcategories.table' ,compact('blogcategories'))->render() ;
            return response()->json(['html' => $html]);
        }
        return view('admin.blogcategories.index');
    }

    public function create()
    {
        return view('admin.blogcategories.create');
    }


    public function store(Store $request)
    {
        $data = $request->validated();
        $data['is_active'] = (int) $request->input('is_active', 0);

        BlogCategory::create($data);
        Report::addToLog('اضافه تصنيف-المدونه');
        return response()->json(['url' => route('admin.blogcategories.index')]);
    }
    public function edit($id)
    {
        
        $blogcategory = BlogCategory::findOrFail($id);
        return view('admin.blogcategories.edit' , ['blogcategory' => $blogcategory]);
    }

    public function update(Update $request, $id)
    {
        $data = $request->validated();

        $blogcategory = BlogCategory::findOrFail($id);
        $blogcategory->update($data);
        Report::addToLog('تعديل تصنيف-المدونه');
        return response()->json(['url' => route('admin.blogcategories.index')]);
    }

    public function show($id)
    {
        $blogcategory = BlogCategory::with('blogs')->findOrFail($id);
        return view('admin.blogcategories.show' , ['blogcategory' => $blogcategory]);
    }
    public function destroy($id)
    {
        try {
            $category = BlogCategory::findOrFail($id);

            // Check if category has related blogs
            $blogsCount = $category->blogs()->count();

            if ($blogsCount > 0) {
                $categoryName = is_array($category->name) ?
                    ($category->name['ar'] ?? $category->name['en'] ?? 'فئة غير محددة') :
                    $category->name;

                return response()->json([
                    'error' => 'لا يمكن حذف فئة "' . $categoryName . '" لأنها تحتوي على ' . $blogsCount . ' مدونة/مدونات'
                ], 422);
            }

            $category->delete();
            Report::addToLog('حذف تصنيف مدونة');
            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الفئة'], 500);
        }
    }

    public function destroyAll(Request $request)
    {
        try {
            $requestIds = json_decode($request->data);
            $ids = [];

            if (empty($requestIds)) {
                return response()->json(['error' => 'لم يتم تحديد فئات للحذف'], 422);
            }

            foreach ($requestIds as $id) {
                $ids[] = $id->id;
            }

            if (empty($ids)) {
                return response()->json(['error' => 'لم يتم تحديد فئات صحيحة للحذف'], 422);
            }

            // Check if any categories have related blogs
            $categoriesWithBlogs = BlogCategory::whereIn('id', $ids)
                ->whereHas('blogs')
                ->with('blogs')
                ->get();

            if ($categoriesWithBlogs->count() > 0) {
                $categoryNames = $categoriesWithBlogs->pluck('name')->map(function($name) {
                    return is_array($name) ? ($name['ar'] ?? $name['en'] ?? 'فئة غير محددة') : $name;
                })->implode(', ');

                return response()->json([
                    'error' => 'لا يمكن حذف الفئات التالية لأنها تحتوي على مدونات: ' . $categoryNames
                ], 422);
            }

            if (BlogCategory::whereIntegerInRaw('id',$ids)->get()->each->delete()) {
                Report::addToLog('حذف العديد من تصنيفات المدونة') ;
                return response()->json('success');
            } else {
                return response()->json('failed');
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الفئات'], 500);
        }
    }
}
