<?php

namespace App\Http\Controllers\Api\Provider;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Auth\Provider\LoginRequest;
use App\Http\Requests\Api\Auth\Provider\RegisterRequest;
use App\Http\Resources\Api\ProviderResource;
use App\Models\User;
use App\Models\UserUpdate;
use App\Services\AuthService;
use App\Traits\GeneralTrait;
use App\Traits\ResponseTrait;
use App\Traits\SmsTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    use ResponseTrait, SmsTrait, GeneralTrait;

    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(RegisterRequest $request)
    {
        // Prepare user data
        $userData = $request->safe()->only([
            'name', 'country_code', 'phone', 'email', 'password',
            'id_number', 'city_id' , 'region_id' , 'gender'
        ]);
        $userData['type'] = 'provider';

        // Prepare provider data
        $providerData = $request->safe()->only([
            'commercial_name', 'salon_type', 'residence_type',
            'commercial_register_no', 'sponsor_name', 'sponsor_phone',
            'institution_name', 'lat', 'lng' , 'nationality' , 'in_home' , 'in_salon' , 'home_fees' , 'id_number'
        ]);

        // Prepare bank data
        $bankData = $request->safe()->only([
            'holder_name', 'bank_name', 'account_number', 'iban'
        ]);
        $bankData['is_default'] = true;

        // Prepare media files
        $mediaFiles = [
            'commercial_register_image' => $request->file('commercial_register_image'),
            'logo' => $request->file('logo'),
            'residence_image' => $request->file('residence_image'),
            'id_image' => $request->file('id_image'),
            'freelance_document_image' => $request->file('freelance_document_image'),
        ];

        // Register provider using service
        $user = $this->authService->registerProvider(
            $userData,
            $providerData,
            $bankData,
            $mediaFiles
        );

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        // Return response
        $userData = new ProviderResource($user);
        return Responder::success(['user' => $userData], ['message' => __('auth.registered')]);
    }

    public function login(LoginRequest $request)
    {
        $user = User::where('phone', $request->phone)
            ->where('type', 'provider')
            ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return Responder::error(__('auth.failed'));
        }
  if ($user->status == 'blocked') {
            $user->logout();
            return Responder::error(__('auth.blocked'), [], 423);
        }

          if ($user->provider->status == 'rejected') {
            $user->logout();
            return Responder::error(__('auth.rejected'), [], 423);
        }



        if (!$user->is_active) {
            $data = $user->sendVerificationCode();
            return Responder::error(__('auth.not_active'), [], 203);
        }

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        // Check if user is not approved by admin
        if ($user->provider && $user->provider->status == 'in_review') {
            return Responder::error(__('auth.not_approved'), [], 403);
        }

        return Responder::success(['user' => $user->login()], ['message' => __('apis.signed')]);
    }

    public function getProfile(Request $request)
    {
        $user = Auth::user();

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        $requestToken = ltrim($request->header('authorization'), 'Bearer ');
        $userData = ProviderResource::make($user)->setToken($requestToken);

        return Responder::success($userData);
    }

    public function updateProfile(\App\Http\Requests\Api\Auth\Provider\UpdateProfileRequest $request)
    {
        $user = Auth::user();


        if (!$user->is_active) {
            return $this->phoneActivationReturn($user);
          }

        $data = $request->validated();


        // Prepare user data
        $userData = $request->safe()->only([
            'name', 'country_code', 'phone', 'email', 'password',
            'id_number', 'city_id' , 'region_id' , 'gender'
        ]);

        // Prepare provider data
        $providerData = $request->safe()->only([
            'commercial_name', 'salon_type', 'residence_type',
            'commercial_register_no', 'sponsor_name', 'sponsor_phone',
            'institution_name', 'lat', 'lng' , 'nationality' , 'in_home' , 'in_salon' , 'home_fees' , 'facebook' , 'instagram' , 'whatsapp' , 'id_number' , 'id_image'

        ]);

        // Prepare bank data
        $bankData = $request->safe()->only([
            'holder_name', 'bank_name', 'account_number', 'iban'
        ]);

        // Prepare media files
        $mediaFiles = [];

        if ($request->hasFile('logo')) {
            $mediaFiles['logo'] = $request->file('logo');
        }

        if ($request->hasFile('salon_images')) {
            $mediaFiles['salon_images'] = $request->file('salon_images');
        }

        if ($request->hasFile('id_image')) {
            $mediaFiles['id_image'] = $request->file('id_image');
        }

        if ($request->hasFile('freelance_document_image')) {
            $mediaFiles['freelance_document_image'] = $request->file('freelance_document_image');
        }

        // Update provider profile using service
        $user = $this->authService->updateProviderProfile(
            $user,
            $userData,
            $providerData,
            $bankData,
            $mediaFiles
        );

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        $requestToken = ltrim($request->header('authorization'), 'Bearer ');
        $userData = ProviderResource::make($user->refresh())->setToken($requestToken);

     

        return Responder::success(['user' => $userData], ['message' => __('apis.updated')]);
    }

    public function switchOrdersStatus() {
        $user = Auth::user()->provider;
        if($user->products->count() == 0 || $user->services->count() == 0 ){
            return Responder::error(__('apis.orders_const'));
        }
        if($user->workingHours->count() == 0)
        {
            return Responder::error(__('apis.work_hours'));
        }
        $user->update(['accept_orders' => !$user->accept_orders]);
        return Responder::success(['accept_orders' => (bool) $user->refresh()->accept_orders], ['message' => __('apis.updated')]);
    }

    public function home()
    {
        $user = Auth::user();
        $provider = $user->provider;
        
        if (!$provider) {
            return Responder::error(__('apis.provider_not_found'));
        }
        
        // Get all provider sub-orders
        $providerSubOrders = $provider->providerSubOrders()
            ->with(['order.items'])
            ->get();
        
        // Initialize counters and totals
        $totalOrders = $providerSubOrders->count();
        $servicesOnlyCount = 0;
        $productsOnlyCount = 0;
        $completedOrdersCount = 0;
        $canceledOrdersCount = 0;
        $totalAmount = 0;
        $servicesOnlyAmount = 0;
        $productsOnlyAmount = 0;
        
        foreach ($providerSubOrders as $subOrder) {
            $order = $subOrder->order;
            if (!$order || !$order->items) {
                continue;
            }
            
            // Count completed and canceled orders
            if ($subOrder->status === 'completed') {
                $completedOrdersCount++;
            } elseif ($subOrder->status === 'cancelled') {
                $canceledOrdersCount++;
            }
            
            // Get items for this specific provider from the order
            $providerItems = $order->items->filter(function($item) use ($provider) {
                if (!$item->item) {
                    return false;
                }
                
                // Check if the item belongs to this provider
                if (isset($item->item->provider_id)) {
                    return $item->item->provider_id == $provider->id;
                }
                
                return false;
            });
            
            if ($providerItems->isEmpty()) {
                continue;
            }
            
            // Calculate total amount for this provider's items
            $providerItemsTotal = $providerItems->sum('total');
            $totalAmount += $providerItemsTotal;
            
            // Check if order has only services
            $hasServices = $providerItems->where('item_type', 'App\Models\Service')->isNotEmpty();
            $hasProducts = $providerItems->where('item_type', 'App\Models\Product')->isNotEmpty();
            
            if ($hasServices && !$hasProducts) {
                $servicesOnlyCount++;
                $servicesOnlyAmount += $providerItemsTotal;
            } elseif ($hasProducts && !$hasServices) {
                $productsOnlyCount++;
                $productsOnlyAmount += $providerItemsTotal;
            }
        }
        
        return Responder::success([
            'accept_orders' => (bool) $provider->accept_orders,
            'statistics' => [
                'total_orders' => $totalOrders,
                'services_only_count' => $servicesOnlyCount,
                'products_only_count' => $productsOnlyCount,
                'completed_orders_count' => $completedOrdersCount,
                'canceled_orders_count' => $canceledOrdersCount,
                'total_amount' => round($totalAmount, 2),
                'services_only_amount' => round($servicesOnlyAmount, 2),
                'products_only_amount' => round($productsOnlyAmount, 2),
            ]
        ]);
    }

    
}


