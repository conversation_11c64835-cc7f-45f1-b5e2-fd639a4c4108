<?php $__env->startSection('content'); ?>
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="coupon match-height">
        <div class="col-12">
            <div class="card">
                
                <div class="card-content">
                    <div class="card-body">
                        <form  class="store form-horizontal" >
                            <div class="form-body">
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.coupon_number')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="coupon_num" value="<?php echo e($coupon->coupon_num); ?>" class="form-control" placeholder="<?php echo e(__('admin.enter_coupon_number')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.number_of_use')); ?></label>
                                            <div class="controls">
                                                <input type="number" name="usage_time" value="<?php echo e($coupon->usage_time); ?>" class="form-control" placeholder="<?php echo e(__('admin.enter_number_of_use')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.discount_type')); ?></label>
                                            <div class="controls">
                                                <select name="type" class="select2 form-control" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                                    <option value><?php echo e(__('admin.select_the_discount_state')); ?></option>
                                                    <option <?php echo e($coupon->type == 'ratio' ? 'selected' : ''); ?> value="ratio"><?php echo e(__('admin.Percentage')); ?></option>
                                                    <option <?php echo e($coupon->type == 'number' ? 'selected' : ''); ?> value="number"><?php echo e(__('admin.fixed_number')); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.discount_value')); ?></label>
                                            <div class="controls">
                                                <input type="number" value="<?php echo e($coupon->discount); ?>" name="discount" class="discount form-control" placeholder="<?php echo e(__('admin.type_the_value_of_the_discount')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.larger_value_for_discount')); ?></label>
                                            <div class="controls">
                                                <input readonly type="number" value="<?php echo e($coupon->max_discount); ?>" name="max_discount" class="max_discount form-control" placeholder="<?php echo e(__('admin.write_the_greatest_value_for_the_discount')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.start_date')); ?></label>
                                            <div class="controls">
                                                <input type="text" value="<?php echo e(date('M,Y d', strtotime($coupon->start_date))); ?>" name="start_date" class="form-control" required
                                                    data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.expiry_date')); ?></label>
                                            <div class="controls">
                                                <input  type="text" value="<?php echo e(date('M,Y d', strtotime($coupon->expire_date))); ?>" name="expire_date" class="pickadate form-control"  required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                    
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script>
        $('.store input').attr('disabled' , true)
        $('.store textarea').attr('disabled' , true)
        $('.store select').attr('disabled' , true)

    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/coupons/show.blade.php ENDPATH**/ ?>