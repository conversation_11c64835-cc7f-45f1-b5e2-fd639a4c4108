<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\products\Store;
use App\Http\Requests\Admin\products\Update;
use App\Models\Product;
use App\Models\Provider;
use App\Models\ProductCategory;
use App\Traits\Report;


class ProductController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {
            $products = Product::with(['provider.user', 'category'])
                ->search(request()->searchArray)
                ->paginate(30);
            $html = view('admin.products.table', compact('products'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.products.index');
    }

    public function create()
    {
        $providers = Provider::with('user')->get();
        $categories = ProductCategory::where('is_active', 1)->get();

        return view('admin.products.create', compact('providers', 'categories'));
    }


    public function store(Store $request)
    {
            $product = Product::create($request->validated());

            // Handle image upload using Spatie Media Library
            if ($request->hasFile('image')) {
                $product->addMediaFromRequest('image')
                    ->usingName('product_image')
                    ->usingFileName(time() . '_product_' . $product->id . '.' . $request->file('image')->getClientOriginalExtension())
                    ->toMediaCollection('product-images');
            }

            // Log success
            Report::addToLog('اضافه منتج');

            return response()->json(['url' => route('admin.products.index')]);

    }
    public function edit($id)
    {
        $product = Product::findOrFail($id);
        $providers = Provider::with('user')->get();
        $categories = ProductCategory::where('is_active', 1)->get();

        return view('admin.products.edit', compact('product', 'providers', 'categories'));
    }

    public function update(Update $request, $id)
    {
            // Find the product
            $product = Product::findOrFail($id);

            // Update the product
            $product->update($request->validated());

            // Handle image upload using Spatie Media Library
            if ($request->hasFile('image')) {
                // Clear existing images and add new one
                $product->clearMediaCollection('product-images');
                $product->addMediaFromRequest('image')
                    ->usingName('product_image')
                    ->usingFileName(time() . '_product_' . $product->id . '.' . $request->file('image')->getClientOriginalExtension())
                    ->toMediaCollection('product-images');
            }

            // Log success
            Report::addToLog('تعديل منتج');

            return response()->json(['url' => route('admin.products.index')]);

    }

    public function show($id)
    {
        $product = Product::with(['provider.user', 'category'])->findOrFail($id);
        return view('admin.products.show', compact('product'));
    }
    public function destroy($id)
    {
        try {
            $product = Product::findOrFail($id);

            // Check if product is related to any order items
            $orderItemsCount = $product->orderItems()->count();

            if ($orderItemsCount > 0) {
                return response()->json([
                    'error' => 'لا يمكن حذف هذا المنتج لأنه مرتبط بـ ' . $orderItemsCount . ' طلب/طلبات'
                ], 422);
            }

            $product->delete();
            Report::addToLog('حذف منتج');
            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف المنتج'], 500);
        }
    }

    /**
     * Delete multiple products
     */
    public function deleteAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        $productsWithOrders = Product::whereIn('id', $ids)
        ->whereHas('orderItems')
        ->with('orderItems')
        ->get();

    if ($productsWithOrders->count() > 0) {
        $productNames = $productsWithOrders->pluck('name')->map(function($name) {
            return is_array($name) ? ($name['ar'] ?? $name['en'] ?? 'منتج غير محدد') : $name;
        })->implode(', ');

        return response()->json([
            'error' => 'لا يمكن حذف المنتجات التالية لأنها مرتبطة بطلبات: ' . $productNames
        ], 422);
    }

        if (Product::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من المستخدمين');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }

    /**
     * Toggle product status
     */
    public function toggleStatus(Request $request)
    {
        $product = Product::findOrFail($request->id);
        $product->update(['is_active' => !$product->is_active]);

        $status = $product->is_active ? 'تفعيل' : 'إلغاء تفعيل';
        Report::addToLog($status . ' منتج');

        return response()->json([
            'status' => $product->is_active,
            'message' => 'تم تحديث حالة المنتج بنجاح'
        ]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (Product::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من المستخدمين');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }

}
