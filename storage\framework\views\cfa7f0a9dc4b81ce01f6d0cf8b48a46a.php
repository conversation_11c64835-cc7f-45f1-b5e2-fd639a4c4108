<?php $__env->startSection('content'); ?>
<form  class="store form-horizontal" >
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row">
        <div class="col-md-3">
            <div class="col-12 card card-body">
                <div class="imgMontg col-12 text-center">
                    <div class="dropBox">
                        <div class="textCenter">
                            <div class="imagesUploadBlock">
                                <label class="uploadImg">
                                    <span><i class="feather icon-image"></i></span>
                                    <input type="file" accept="image/*" name="image" class="imageUploader">
                                </label>
                                <div class="uploadedBlock">
                                    <img src="<?php echo e($image->image_ar); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="textCenter">
                            <div class="imagesUploadBlock">
                                <label class="uploadImg">
                                    <span><i class="feather icon-image"></i></span>
                                    <input type="file" accept="image/*" name="image" class="imageUploader">
                                </label>
                                <div class="uploadedBlock">
                                    <img src="<?php echo e($image->image_en); ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-9">
            <div class="card">
                
                <div class="card-content">
                    <div class="card-body">
                            <div class="form-body">
                                <div class="row">
                                

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.from_date')); ?></label>
                                            <div class="controls">
                                                <input type="date" name="start_date" value="<?php echo e($image->start_date); ?>" class="form-control" placeholder="<?php echo e(__('admin.from_date')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.to_date')); ?></label>
                                            <div class="controls">
                                                <input type="date" name="end_date" value="<?php echo e($image->end_date); ?>" class="form-control" placeholder="<?php echo e(__('admin.to_date')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.link')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="link" value="<?php echo e($image->link); ?>"  class="form-control" placeholder="<?php echo e(__('admin.link')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                    
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
</form>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script>
        $('.store input').attr('disabled' , true)
        $('.store textarea').attr('disabled' , true)
        $('.store select').attr('disabled' , true)

    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/images/show.blade.php ENDPATH**/ ?>