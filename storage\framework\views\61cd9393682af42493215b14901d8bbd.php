

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="<?php echo e(route('admin.shortvideos.update' , ['id' => $shortvideo->id])); ?>" class="store form-horizontal" novalidate enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="form-body">
                                <div class="row">

                                    
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label><?php echo e(__('admin.current_video')); ?></label>
                                            <div class="current-video mb-3">
                                                <?php
                                                    $videoMedia = $shortvideo->getFirstMediaUrl('short_video');
                                                ?>
                                                <?php if($videoMedia): ?>
                                                    <div class="video-preview">
                                                        <video width="300" height="200" controls>
                                                            <source src="<?php echo e($videoMedia); ?>" type="video/mp4">
                                                            Your browser does not support the video tag.
                                                        </video>
                                                        <p class="text-muted mt-2"><?php echo e(__('admin.current_video_file')); ?></p>
                                                    </div>
                                                <?php else: ?>
                                                    <p class="text-muted"><?php echo e(__('admin.no_video_uploaded')); ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="video"><?php echo e(__('admin.upload_new_video')); ?> (<?php echo e(__('admin.optional')); ?>)</label>
                                            <div class="controls">
                                                <input type="file" name="video" class="form-control" accept="video/*">
                                                <small class="text-muted"><?php echo e(__('admin.supported_formats')); ?>: MP4, MOV, AVI, WMV, FLV, WEBM (<?php echo e(__('admin.max_size')); ?>: 100MB)</small>
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="video_id"><?php echo e(__('admin.video_id')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="video_id" value="<?php echo e($shortvideo->video_id); ?>" class="form-control" placeholder="<?php echo e(__('admin.video_id')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="client_name"><?php echo e(__('admin.client_name')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="client_name" value="<?php echo e($shortvideo->client_name); ?>" class="form-control" placeholder="<?php echo e(__('admin.client_name')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="order_rate_id"><?php echo e(__('admin.order_rate')); ?></label>
                                            <div class="controls">
                                                <select name="order_rate_id" class="form-control select2" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    <option value=""><?php echo e(__('admin.choose')); ?></option>
                                                    <?php $__currentLoopData = \App\Models\OrderRate::with(['order', 'user'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $orderRate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($orderRate->id); ?>" <?php echo e($shortvideo->order_rate_id == $orderRate->id ? 'selected' : ''); ?>>
                                                            <?php echo e(__('admin.order')); ?> #<?php echo e($orderRate->order->order_number ?? $orderRate->id); ?> - <?php echo e($orderRate->user->name ?? 'N/A'); ?>

                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="rate_id"><?php echo e(__('admin.rate')); ?> (<?php echo e(__('admin.optional')); ?>)</label>
                                            <div class="controls">
                                                <select name="rate_id" class="form-control select2">
                                                    <option value=""><?php echo e(__('admin.choose')); ?></option>
                                                    <?php $__currentLoopData = \App\Models\Rate::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($rate->id); ?>" <?php echo e($shortvideo->rate_id == $rate->id ? 'selected' : ''); ?>>
                                                            <?php echo e($rate->rate); ?> <?php echo e(__('admin.stars')); ?> - <?php echo e($rate->comment); ?>

                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="user_id"><?php echo e(__('admin.user')); ?> (<?php echo e(__('admin.optional')); ?>)</label>
                                            <div class="controls">
                                                <select name="user_id" class="form-control select2">
                                                    <option value=""><?php echo e(__('admin.choose')); ?></option>
                                                    <?php $__currentLoopData = \App\Models\User::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($user->id); ?>" <?php echo e($shortvideo->user_id == $user->id ? 'selected' : ''); ?>>
                                                            <?php echo e($user->name); ?> (<?php echo e($user->email); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="published_at"><?php echo e(__('admin.published_at')); ?></label>
                                            <div class="controls">
                                                <input type="datetime-local" name="published_at" value="<?php echo e($shortvideo->published_at ? \Carbon\Carbon::parse($shortvideo->published_at)->format('Y-m-d\TH:i') : ''); ?>" class="form-control">
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="expired_at"><?php echo e(__('admin.expired_at')); ?></label>
                                            <div class="controls">
                                                <input type="datetime-local" name="expired_at" value="<?php echo e($shortvideo->expired_at ? \Carbon\Carbon::parse($shortvideo->expired_at)->format('Y-m-d\TH:i') : ''); ?>" class="form-control">
                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="is_active"><?php echo e(__('admin.status')); ?></label>
                                            <div class="controls">
                                                <select name="is_active" class="form-control" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    <option value="1" <?php echo e($shortvideo->is_active ? 'selected' : ''); ?>><?php echo e(__('admin.active')); ?></option>
                                                    <option value="0" <?php echo e(!$shortvideo->is_active ? 'selected' : ''); ?>><?php echo e(__('admin.inactive')); ?></option>
                                                </select>
                                            </div>
                                        </div>

                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.update')); ?></button>
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    
    
        <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
        <?php echo $__env->make('admin.shared.submitEditForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/shortvideos/edit.blade.php ENDPATH**/ ?>