@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                {{-- <div class="card-header">
                    <h4 class="card-title">{{__('admin.add')}}</h4>
                </div> --}}
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="{{route('admin.fqs.store')}}" class="store form-horizontal" novalidate>
                            @csrf
                            <div class="form-body">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="col-12">
                                            <ul class="nav nav-tabs mb-3">
                                                @foreach (languages() as $lang)
                                                    <li class="nav-item">
                                                        <a class="nav-link @if($loop->first) active @endif"  data-toggle="pill" href="#first_{{$lang}}" aria-expanded="true">{{  __('admin.data') }} {{ $lang }}</a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>  

                                        <div class="tab-content">
                                            @foreach (languages() as $lang)
                                                <div role="tabpanel" class="tab-pane fade @if($loop->first) show active @endif " id="first_{{$lang}}" aria-labelledby="first_{{$lang}}" aria-expanded="true">
                                                    <div class="col-md-12 col-12">
                                                        <div class="form-group">
                                                            <label for="first-name-column">{{__('admin.question')}} {{ $lang }}</label>
                                                            <div class="controls">
                                                                <input type="text" name="question[{{$lang}}]" class="form-control" placeholder="{{__('admin.write') . __('admin.question')}} {{ $lang }}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="controls">
                                                                <label for="account-name">{{__('admin.answer')}} {{ $lang }}</label>
                                                                <textarea class="form-control" name="answer[{{$lang}}]" id="" cols="30" rows="10"  required data-validation-required-message="{{__('admin.this_field_is_required')}}"  placeholder="{{__('admin.write') . __('admin.answer')}} {{ $lang }} "></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>

                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="account-name">{{__('admin.audience_type')}}</label>
                                                <select class="form-control" name="audience_type" id="audience_type">
                                                    <option value="client">{{__('admin.client')}}</option>
                                                    <option value="provider">{{__('admin.provider')}}</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{__('admin.add')}}</button>
                                        <a href="{{ url()->previous() }}" type="reset" class="btn btn-outline-warning mr-1 mb-1">{{__('admin.back')}}</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection
@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')}}"></script>
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>
    
    {{-- show selected image script --}}
        @include('admin.shared.addImage')
    {{-- show selected image script --}}

    {{-- submit add form script --}}
        @include('admin.shared.submitAddForm')
    {{-- submit add form script --}}

    <script>
        $(document).ready(function() {
            // Custom validation for English data requirement
            $('form.store').on('submit', function(e) {
                var hasArabicData = false;
                var hasEnglishData = false;

                // Check if Arabic data is filled
                if ($('input[name="question[ar]"]').val().trim() !== '' || $('textarea[name="answer[ar]"]').val().trim() !== '') {
                    hasArabicData = true;
                }

                // Check if English data is filled
                if ($('input[name="question[en]"]').val().trim() !== '' || $('textarea[name="answer[en]"]').val().trim() !== '') {
                    hasEnglishData = true;
                }

                // If Arabic data is filled but English is not, show alert
                if (hasArabicData && !hasEnglishData) {
                    e.preventDefault();
                    Swal.fire({
                        position: 'top-start',
                        type: 'warning',
                        title: 'تنبيه',
                        text: 'البيانات الإنجليزية مطلوبة لسهولة التعامل مع الموقع',
                        showConfirmButton: true,
                        confirmButtonClass: 'btn btn-primary',
                        buttonsStyling: false,
                    });
                    return false;
                }
            });
        });
    </script>

@endsection