<?php
namespace App\Http\Requests\Admin\Provider;

use App\Rules\EmailUniqueWhenNotNull;
use App\Rules\ProviderPhoneUnique;
use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // User basic fields
            'name'                       => 'required|max:50',
            'country_code'               => 'required|numeric|digits_between:2,5',
            'phone'                      => [
                'required',
                new ProviderPhoneUnique(),
                function ($attribute, $value, $fail) {
                    // Check if phone belongs to a rejected provider
                    $rejectedProvider = \App\Models\User::where('phone', $value)
                        ->where('type', 'provider')
                        ->whereHas('provider', function ($query) {
                            $query->where('status', 'rejected');
                        })
                        ->first();

                    if ($rejectedProvider) {
                        $fail(__('admin.phone_rejected_cannot_reregister'));
                    }
                },
            ],
            'email' => [
                'sometimes',
                'nullable',
                'email',
                'max:50',
                EmailUniqueWhenNotNull::forUsers(),
            ],
            'password'                   => 'required|confirmed|min:8|max:100',
            'city_id'                    => 'required|exists:cities,id',
            'gender'                     => 'required|in:male,female',
            'type'                       => 'sometimes',
            'image'                      => 'nullable|image|max:2048',

            // Provider specific fields
            'commercial_name'            => 'required|array',
            'commercial_name.ar'         => 'required|string|max:100',
            'commercial_name.en'         => 'nullable|string|max:100',
            'salon_type'                 => 'required|string|in:salon,beauty_center,freelancer',
            'residence_type'             => 'required_if:nationality,other|nullable|string|in:individual,professional',
            'nationality'                => 'required|string|in:saudi,other',
            'logo'                       => 'required|image|max:2048',
            'commercial_register_no'     => 'required_unless:salon_type,freelancer|string|min:10|max:50',
            'commercial_register_image'  => 'required_unless:salon_type,freelancer|image|max:2048',
            'sponsor_name'               => 'required_if:nationality,other|nullable|string|max:100',
            'sponsor_phone'              => 'required_if:nationality,other|nullable|string|max:20',
            'institution_name'           => 'required_unless:salon_type,freelancer|string|max:100',
            'residence_image'            => 'required_if:nationality,other|nullable|image|max:2048',
            'in_home'                    => 'sometimes|boolean',
            'in_salon'                   => 'sometimes|boolean',
            'comission'                  => 'sometimes|numeric|min:0|max:999999.99',
            'salon_images'               => 'sometimes|nullable|array|max:5',
            'salon_images.*'             => 'image',

            // Freelancer specific fields
            'freelance_document'         => 'required_if:salon_type,freelancer|nullable|string|max:100',
            'freelance_document_image'   => 'required_if:salon_type,freelancer|nullable|image|max:2048',

            // Working hours validation (optional)
            'working_hours'              => 'sometimes|nullable|array',
            'working_hours.*.day'        => 'sometimes|string|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
            'working_hours.*.start_time' => 'required_if:working_hours.*.is_working,1|nullable',
            'working_hours.*.end_time'   => 'required_if:working_hours.*.is_working,1|nullable|after:working_hours.*.start_time',
            'working_hours.*.is_working' => 'sometimes|boolean',
        ];

        // Add conditional validation for freelancer ID fields
        if ($this->salon_type === 'freelancer' && $this->nationality === 'saudi') {
            $rules['id_number'] = 'required|string|size:10';
            $rules['id_image'] = 'required|image|max:2048';
        } else {
            $rules['id_number'] = 'nullable|string|size:10';
            $rules['id_image'] = 'nullable|image|max:2048';
        }

        return $rules;
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required'                          => __('admin.name') . ' ' . __('admin.this_field_is_required'),
            'name.max'                               => __('admin.name') . ' ' . __('admin.max_length_50'),
            'country_code.required'                  => __('admin.country_code') . ' ' . __('admin.this_field_is_required'),
            'country_code.numeric'                   => __('admin.country_code') . ' ' . __('admin.must_be_numeric'),
            'country_code.digits_between'            => __('admin.country_code') . ' ' . __('admin.country_code_digits_between_2_5'),
            'phone.required'                         => __('admin.phone_number') . ' ' . __('admin.this_field_is_required'),
            'phone.min'                              => __('admin.phone_number') . ' ' . __('admin.phone_min_8_digits'),
            'phone.unique'                           => __('admin.phone_number') . ' ' . __('admin.phone_already_exists'),
            'email.required'                         => __('admin.email') . ' ' . __('admin.this_field_is_required'),
            'email.email'                            => __('admin.email') . ' ' . __('admin.email_formula_is_incorrect'),
            'email.max'                              => __('admin.email') . ' ' . __('admin.max_length_191'),
            'email.unique'                           => __('admin.email') . ' ' . __('admin.email_already_exists'),
            'password.required'                      => __('admin.password') . ' ' . __('admin.this_field_is_required'),
            'password.min'                           => __('admin.password') . ' ' . __('admin.password_min_6_characters'),
            'password.confirmed'                     => __('admin.password') . ' ' . __('admin.password_confirmation_does_not_match'),
            'city_id.required'                       => __('admin.City') . ' ' . __('admin.this_field_is_required'),
            'city_id.exists'                         => __('admin.City') . ' ' . __('admin.city_not_found'),
            'gender.required'                        => __('admin.gender') . ' ' . __('admin.this_field_is_required'),
            'gender.in'                              => __('admin.gender') . ' ' . __('admin.invalid_gender'),
            'status.required'                        => __('admin.status') . ' ' . __('admin.this_field_is_required'),
            'status.in'                              => __('admin.status') . ' ' . __('admin.invalid_status'),
            'image.image'                            => __('admin.profile_image') . ' ' . __('admin.must_be_image'),
            'image.max'                              => __('admin.profile_image') . ' ' . __('admin.image_max_size_2mb'),
            'commercial_name.required'               => __('admin.commercial_name') . ' ' . __('admin.this_field_is_required'),
            'commercial_name.array'                  => __('admin.commercial_name') . ' ' . __('admin.invalid_format'),
            'commercial_name.ar.required'            => __('admin.commercial_name') . ' (' . __('admin.arabic') . ') ' . __('admin.this_field_is_required'),
            'commercial_name.ar.string'              => __('admin.commercial_name') . ' (' . __('admin.arabic') . ') ' . __('admin.must_be_text'),
            'commercial_name.ar.max'                 => __('admin.commercial_name') . ' (' . __('admin.arabic') . ') ' . __('admin.max_length_100'),
            'commercial_name.en.string'              => __('admin.commercial_name') . ' (' . __('admin.english') . ') ' . __('admin.must_be_text'),
            'commercial_name.en.max'                 => __('admin.commercial_name') . ' (' . __('admin.english') . ') ' . __('admin.max_length_100'),
            'commercial_name.max'                    => __('admin.commercial_name') . ' ' . __('admin.max_length_191'),
            'salon_type.required'                    => __('admin.salon_type') . ' ' . __('admin.this_field_is_required'),
            'salon_type.in'                          => __('admin.salon_type') . ' ' . __('admin.invalid_salon_type'),
            'nationality.required'                   => __('admin.nationality') . ' ' . __('admin.this_field_is_required'),
            'nationality.required_unless'            => __('admin.nationality') . ' ' . __('admin.this_field_is_required'),
            'nationality.in'                         => __('admin.nationality') . ' ' . __('admin.invalid_nationality'),
            'nationality.max'                        => __('admin.nationality') . ' ' . __('admin.max_length_100'),
            'residence_type.required_if'             => __('admin.residence_type') . ' ' . __('admin.this_field_is_required'),
            'residence_type.in'                      => __('admin.residence_type') . ' ' . __('admin.invalid_residence_type'),
            'commercial_register_no.required'        => __('admin.commercial_register_no') . ' ' . __('admin.this_field_is_required'),
            'commercial_register_no.required_unless' => __('admin.commercial_register_no') . ' ' . __('admin.this_field_is_required'),
            'commercial_register_no.string'          => __('admin.commercial_register_no') . ' ' . __('admin.must_be_text'),
            'commercial_register_no.min'             => __('admin.commercial_register_no') . ' ' . __('admin.min_length_10'),
            'commercial_register_no.max'             => __('admin.commercial_register_no') . ' ' . __('admin.max_length_50'),
            'logo.required'                          => __('admin.logo') . ' ' . __('admin.this_field_is_required'),
            'logo.image'                             => __('admin.logo') . ' ' . __('admin.must_be_image'),
            'logo.max'                               => __('admin.logo') . ' ' . __('admin.image_max_size_2mb'),
            'commercial_register_image.required'     => __('admin.commercial_register_image') . ' ' . __('admin.this_field_is_required'),
            'commercial_register_image.required_unless' => __('admin.commercial_register_image') . ' ' . __('admin.this_field_is_required'),
            'commercial_register_image.image'        => __('admin.commercial_register_image') . ' ' . __('admin.must_be_image'),
            'commercial_register_image.max'          => __('admin.commercial_register_image') . ' ' . __('admin.image_max_size_2mb'),
            'sponsor_name.required_if'               => __('admin.sponsor_name') . ' ' . __('admin.this_field_is_required'),
            'sponsor_name.string'                    => __('admin.sponsor_name') . ' ' . __('admin.must_be_text'),
            'sponsor_name.max'                       => __('admin.sponsor_name') . ' ' . __('admin.max_length_100'),
            'sponsor_phone.required_if'              => __('admin.sponsor_phone') . ' ' . __('admin.this_field_is_required'),
            'sponsor_phone.string'                   => __('admin.sponsor_phone') . ' ' . __('admin.must_be_text'),
            'sponsor_phone.max'                      => __('admin.sponsor_phone') . ' ' . __('admin.max_length_20'),
            'institution_name.required'              => __('admin.institution_name') . ' ' . __('admin.this_field_is_required'),
            'institution_name.required_unless'       => __('admin.institution_name') . ' ' . __('admin.this_field_is_required'),
            'institution_name.string'                => __('admin.institution_name') . ' ' . __('admin.must_be_text'),
            'institution_name.max'                   => __('admin.institution_name') . ' ' . __('admin.max_length_100'),
            'description.string'                     => __('admin.description') . ' ' . __('admin.must_be_text'),
            'description.max'                        => __('admin.description') . ' ' . __('admin.max_length_1000'),
            'home_fees.numeric'                      => __('admin.home_fees') . ' ' . __('admin.must_be_numeric'),
            'home_fees.min'                          => __('admin.home_fees') . ' ' . __('admin.must_be_positive'),
            'home_fees.max'                          => __('admin.home_fees') . ' ' . __('admin.max_amount_999999'),
            'mobile_service_fee.numeric'             => __('admin.mobile_service_fee') . ' ' . __('admin.must_be_numeric'),
            'mobile_service_fee.min'                 => __('admin.mobile_service_fee') . ' ' . __('admin.must_be_positive'),
            'mobile_service_fee.max'                 => __('admin.mobile_service_fee') . ' ' . __('admin.max_amount_999999'),
            'residence_image.required_if'            => __('admin.residence_image') . ' ' . __('admin.this_field_is_required'),
            'residence_image.image'                  => __('admin.residence_image') . ' ' . __('admin.must_be_image'),
            'residence_image.max'                    => __('admin.residence_image') . ' ' . __('admin.image_max_size_2mb'),

            // Freelancer specific validation messages
            'freelance_document.required_if'         => __('admin.freelance_document') . ' ' . __('admin.this_field_is_required'),
            'freelance_document.string'              => __('admin.freelance_document') . ' ' . __('admin.must_be_text'),
            'freelance_document.max'                 => __('admin.freelance_document') . ' ' . __('admin.max_length_100'),
            'freelance_document_image.required_if'   => __('admin.freelance_document_image') . ' ' . __('admin.this_field_is_required'),
            'freelance_document_image.image'         => __('admin.freelance_document_image') . ' ' . __('admin.must_be_image'),
            'freelance_document_image.max'           => __('admin.freelance_document_image') . ' ' . __('admin.image_max_size_2mb'),
            'id_number.required'                     => __('admin.id_number') . ' ' . __('admin.this_field_is_required'),
            'id_number.required_if'                  => __('admin.id_number') . ' ' . __('admin.this_field_is_required'),
            'id_number.string'                       => __('admin.id_number') . ' ' . __('admin.must_be_text'),
            'id_number.size'                         => __('admin.id_number') . ' ' . __('admin.id_number_must_be_10_digits'),
            'id_image.required'                      => __('admin.id_image') . ' ' . __('admin.this_field_is_required'),
            'id_image.required_if'                   => __('admin.id_image') . ' ' . __('admin.this_field_is_required'),
            'id_image.image'                         => __('admin.id_image') . ' ' . __('admin.must_be_image'),
            'id_image.max'                           => __('admin.id_image') . ' ' . __('admin.image_max_size_2mb'),

            // Working hours validation messages
            'working_hours.array'                    => __('admin.working_hours') . ' ' . __('admin.working_hours_must_be_array'),
            'working_hours.*.day.in'                 => __('admin.working_hours') . ' ' . __('admin.invalid_day'),
            'working_hours.*.start_time.required_if' => __('admin.start_time') . ' ' . __('admin.start_time_is_required'),
            'working_hours.*.end_time.required_if'   => __('admin.end_time') . ' ' . __('admin.end_time_is_required'),
            'working_hours.*.end_time.after'         => __('admin.end_time') . ' ' . __('admin.end_time_must_be_after_start_time'),
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Convert checkbox values to boolean
        $this->merge([
            'in_home'   => $this->has('in_home') ? true : false,
            'in_salon'  => $this->has('in_salon') ? true : false,
            'is_mobile' => $this->has('is_mobile') ? true : false,
        ]);

        // Fix phone number format if needed
        if ($this->has('phone')) {
            $this->merge([
                'phone' => ltrim($this->phone, '0'),
            ]);
        }

        // Clean up working hours data - only keep enabled days
        if ($this->has('working_hours')) {
            $workingHours = [];
            foreach ($this->working_hours as $day => $workingHour) {
                // Only include days that are marked as working
                if (isset($workingHour['is_working']) && $workingHour['is_working']) {
                    $workingHours[$day] = [
                        'day'        => $workingHour['day'],
                        'start_time' => $workingHour['start_time'],
                        'end_time'   => $workingHour['end_time'],
                        'is_working' => true,
                    ];
                }
            }
            $this->merge(['working_hours' => $workingHours]);
        }
    }
}
