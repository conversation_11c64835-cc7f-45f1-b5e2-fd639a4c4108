

<?php $__env->startSection('css'); ?>
<style>
    .rating-display {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }
    .rating-stars {
        display: flex;
        gap: 2px;
    }
    .star {
        color: #ddd;
        font-size: 20px;
    }
    .star.filled {
        color: #ffc107;
    }
    .star.half {
        background: linear-gradient(90deg, #ffc107 50%, #ddd 50%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    .rating-number {
        font-weight: bold;
        font-size: 18px;
        color: #495057;
    }
    .rating-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .rating-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
    }
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 500;
        text-transform: uppercase;
        font-size: 12px;
    }
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    .status-approved {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .image-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    .image-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .image-item img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    .image-item:hover img {
        transform: scale(1.05);
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Order Rate Details -->
<section id="order-rate-details">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8 col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title"><?php echo e(__('admin.order_rate_details')); ?></h4>
                    <div class="d-flex align-items-center gap-3">
                        <span class="status-badge status-<?php echo e($orderrate->status); ?>">
                            <?php echo e(ucfirst(__('admin.' . $orderrate->status))); ?>

                        </span>
                    </div>
                </div>
                <div class="card-content">
                    <div class="card-body">

                        <!-- Order and User Info -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="text-muted"><?php echo e(__('admin.order_information')); ?></h6>
                                <p><strong><?php echo e(__('admin.order_number')); ?>:</strong> <?php echo e($orderrate->order->order_number ?? 'N/A'); ?></p>
                                <p><strong><?php echo e(__('admin.order_date')); ?>:</strong> <?php echo e($orderrate->order->created_at->format('Y-m-d H:i') ?? 'N/A'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted"><?php echo e(__('admin.customer_information')); ?></h6>
                                <p><strong><?php echo e(__('admin.customer_name')); ?>:</strong> <?php echo e($orderrate->user->name ?? 'N/A'); ?></p>
                                <p><strong><?php echo e(__('admin.review_date')); ?>:</strong> <?php echo e($orderrate->created_at->format('Y-m-d H:i')); ?></p>
                            </div>
                        </div>

                        <!-- Ratings Section -->
                        <div class="rating-card">
                            <h5 class="mb-3"><?php echo e(__('admin.ratings')); ?></h5>

                            <!-- Timing Rate -->
                            <div class="rating-display">
                                <div class="rating-title" style="min-width: 120px;"><?php echo e(__('admin.timing_rate')); ?>:</div>
                                <div class="rating-stars">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= $orderrate->timing_rate): ?>
                                            <span class="star filled">★</span>
                                        <?php elseif($i - 0.5 <= $orderrate->timing_rate): ?>
                                            <span class="star half">★</span>
                                        <?php else: ?>
                                            <span class="star">★</span>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                                <span class="rating-number"><?php echo e($orderrate->timing_rate); ?>/5</span>
                            </div>

                            <!-- Quality Rate -->
                            <div class="rating-display">
                                <div class="rating-title" style="min-width: 120px;"><?php echo e(__('admin.quality_rate')); ?>:</div>
                                <div class="rating-stars">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= $orderrate->quality_rate): ?>
                                            <span class="star filled">★</span>
                                        <?php elseif($i - 0.5 <= $orderrate->quality_rate): ?>
                                            <span class="star half">★</span>
                                        <?php else: ?>
                                            <span class="star">★</span>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                                <span class="rating-number"><?php echo e($orderrate->quality_rate); ?>/5</span>
                            </div>

                            <!-- Service Rate -->
                            <div class="rating-display">
                                <div class="rating-title" style="min-width: 120px;"><?php echo e(__('admin.service_rate')); ?>:</div>
                                <div class="rating-stars">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= $orderrate->service_rate): ?>
                                            <span class="star filled">★</span>
                                        <?php elseif($i - 0.5 <= $orderrate->service_rate): ?>
                                            <span class="star half">★</span>
                                        <?php else: ?>
                                            <span class="star">★</span>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                                <span class="rating-number"><?php echo e($orderrate->service_rate); ?>/5</span>
                            </div>

                            <!-- Average Rating -->
                            <?php
                                $averageRating = ($orderrate->timing_rate + $orderrate->quality_rate + $orderrate->service_rate) / 3;
                            ?>
                            <hr>
                            <div class="rating-display">
                                <div class="rating-title" style="min-width: 120px;"><strong><?php echo e(__('admin.average_rating')); ?>:</strong></div>
                                <div class="rating-stars">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= $averageRating): ?>
                                            <span class="star filled">★</span>
                                        <?php elseif($i - 0.5 <= $averageRating): ?>
                                            <span class="star half">★</span>
                                        <?php else: ?>
                                            <span class="star">★</span>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                                <span class="rating-number"><?php echo e(number_format($averageRating, 1)); ?>/5</span>
                            </div>
                        </div>

                        <!-- Review Body -->
                        <?php if($orderrate->body): ?>
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('admin.review_body')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0"><?php echo e($orderrate->body); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Images Section -->
                        <?php if($orderrate->getMedia('order_rates')->count() > 0): ?>
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><?php echo e(__('admin.images')); ?> (<?php echo e($orderrate->getMedia('order-rates')->count()); ?>)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="image-gallery">
                                        <?php $__currentLoopData = $orderrate->getMedia('order_rates'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="image-item">
                                                <img src="<?php echo e($image->getUrl()); ?>" alt="<?php echo e(__('admin.order_rate_image')); ?>" onclick="openImageModal('<?php echo e($image->getUrl()); ?>', '<?php echo e($image->name); ?>')">
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Videos Section -->
<?php if($orderrate->getMedia('order_rate_videos')->count() > 0): ?>
<div class="card mt-3">
    <div class="card-header">
        <h5 class="card-title mb-0"><?php echo e(__('admin.videos')); ?> (<?php echo e($orderrate->getMedia('order_rate_videos')->count()); ?>)</h5>
    </div>
    <div class="card-body">
        <div class="image-gallery">
            <?php $__currentLoopData = $orderrate->getMedia('order_rate_videos'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $video): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="image-item">
                <video width="100%" height="200" controls>
                    <source src="<?php echo e($video->getUrl()); ?>" type="<?php echo e($video->mime_type); ?>">
                </video>
                <?php if($orderrate->status == 'approved'): ?>
                    <?php if(!$orderrate->shortVideo): ?>
                    <button class="btn btn-success btn-sm mt-2" onclick="publishVideo(<?php echo e($orderrate->id); ?>, <?php echo e($video->id); ?>)">
                        <?php echo e(__('admin.publish_video')); ?>

                    </button>
                    <?php else: ?>
                    <button class="btn btn-secondary btn-sm mt-2" disabled>
                        <?php echo e(__('admin.already_published')); ?>

                    </button>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
        
        </div>
    </div>
</div>
<?php endif; ?>


                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4 col-12">
            <!-- Status Management -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__('admin.status_management')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="status-select"><?php echo e(__('admin.current_status')); ?></label>
                        <select id="status-select" class="form-control" onchange="updateStatus()">
                            <option value="pending" <?php echo e($orderrate->status == 'pending' ? 'selected' : ''); ?>>
                                <?php echo e(__('admin.pending')); ?>

                            </option>
                            <option value="approved" <?php echo e($orderrate->status == 'approved' ? 'selected' : ''); ?>>
                                <?php echo e(__('admin.approved')); ?>

                            </option>
                            <option value="rejected" <?php echo e($orderrate->status == 'rejected' ? 'selected' : ''); ?>>
                                <?php echo e(__('admin.rejected')); ?>

                            </option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <small>
                            <i class="feather icon-info"></i>
                            <?php echo e(__('admin.status_change_note')); ?>

                        </small>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__('admin.quick_stats')); ?></h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo e(__('admin.timing_rate')); ?>:</span>
                        <strong><?php echo e($orderrate->timing_rate); ?>/5</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo e(__('admin.quality_rate')); ?>:</span>
                        <strong><?php echo e($orderrate->quality_rate); ?>/5</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo e(__('admin.service_rate')); ?>:</span>
                        <strong><?php echo e($orderrate->service_rate); ?>/5</strong>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <span><strong><?php echo e(__('admin.average')); ?>:</strong></span>
                        <strong class="text-primary"><?php echo e(number_format(($orderrate->timing_rate + $orderrate->quality_rate + $orderrate->service_rate) / 3, 1)); ?>/5</strong>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo e(__('admin.actions')); ?></h5>
                </div>
                <div class="card-body">
                    <a href="<?php echo e(route('admin.orderrates.index')); ?>" class="btn btn-secondary btn-block mb-2">
                        <i class="feather icon-arrow-left"></i> <?php echo e(__('admin.back_to_list')); ?>

                    </a>
                    <a href="<?php echo e(route('admin.orders.show', $orderrate->order_id)); ?>" class="btn btn-primary btn-block">
                        <i class="feather icon-eye"></i> <?php echo e(__('admin.view_order')); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"><?php echo e(__('admin.image_preview')); ?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Image modal functionality
    function openImageModal(imageUrl, imageName) {
        document.getElementById('modalImage').src = imageUrl;
        document.getElementById('imageModalLabel').textContent = imageName || '<?php echo e(__("admin.image_preview")); ?>';
        $('#imageModal').modal('show');
    }

    // Status update functionality
    function updateStatus() {
        const statusSelect = document.getElementById('status-select');
        const newStatus = statusSelect.value;
        const orderRateId = <?php echo e($orderrate->id); ?>;

        Swal.fire({
            title: '<?php echo e(__("admin.confirm_status_change")); ?>',
            text: '<?php echo e(__("admin.are_you_sure_change_status")); ?>',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '<?php echo e(__("admin.yes_change")); ?>',
            cancelButtonText: '<?php echo e(__("admin.cancel")); ?>'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: '<?php echo e(__("admin.updating")); ?>',
                    text: '<?php echo e(__("admin.please_wait")); ?>',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Make AJAX request
                fetch(`/admin/orderrates/${orderRateId}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ status: newStatus })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: '<?php echo e(__("admin.success")); ?>',
                            text: '<?php echo e(__("admin.status_updated_successfully")); ?>',
                            icon: 'success',
                            confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        throw new Error(data.message || '<?php echo e(__("admin.error_occurred")); ?>');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: '<?php echo e(__("admin.error")); ?>',
                        text: error.message || '<?php echo e(__("admin.error_occurred")); ?>',
                        icon: 'error',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    });
                    // Reset select to original value
                    statusSelect.value = '<?php echo e($orderrate->status); ?>';
                });
            } else {
                // Reset select to original value if cancelled
                statusSelect.value = '<?php echo e($orderrate->status); ?>';
            }
        });
    }
</script>

<script>
  function publishVideo(orderRateId, mediaId) {
    Swal.fire({
        title: '<?php echo e(__("admin.confirm_publish_title")); ?>',
        text: '<?php echo e(__("admin.confirm_publish_text")); ?>',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: '<?php echo e(__("admin.yes_publish")); ?>',
        cancelButtonText: '<?php echo e(__("admin.cancel")); ?>'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/orderrates/${orderRateId}/publish-video`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ media_id: mediaId })
            })
            .then(res => res.json())
            .then(data => {
                if (data.message) {
                    Swal.fire('<?php echo e(__("admin.success")); ?>', data.message, 'success');
                    location.reload()
                }
            })
            .catch(err => {
                Swal.fire('<?php echo e(__("admin.error")); ?>', '<?php echo e(__("admin.error_occurred")); ?>', 'error');
                console.error(err);
            });
        }
    });
}

</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/orderrates/show.blade.php ENDPATH**/ ?>