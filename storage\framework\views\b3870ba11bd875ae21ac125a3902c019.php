

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
<form method="POST" action="<?php echo e(route('admin.images.store')); ?>" class="store form-horizontal" novalidate enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <section id="multiple-column-form">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title"><?php echo e(__('admin.add')); ?></h4>
                    </div>
                    <div class="card-content">
                        <div class="card-body">
                            <div class="form-body">
                                <div class="row">
                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="name_ar"><?php echo e(__('admin.name')); ?> (<?php echo e(__('admin.ar')); ?>)</label>
                                            <input type="text" id="name_ar" class="form-control" name="name[ar]" 
                                                placeholder="<?php echo e(__('admin.write') . __('admin.name')); ?> <?php echo e(__('admin.ar')); ?>"
                                                required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="name_en"><?php echo e(__('admin.name')); ?> (<?php echo e(__('admin.en')); ?>)</label>
                                            <input type="text" id="name_en" class="form-control" name="name[en]" 
                                                placeholder="<?php echo e(__('admin.write') . __('admin.name')); ?> <?php echo e(__('admin.en')); ?>"
                                                required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-12 col-12">
                                        <div class="form-group">
                                            <label for="link"><?php echo e(__('admin.link')); ?></label>
                                            <input type="text" id="link" class="form-control" name="link" placeholder="<?php echo e(__('admin.write') . __('admin.link')); ?>">
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="image_ar"><?php echo e(__('admin.image')); ?> (<?php echo e(__('admin.ar')); ?>)</label>
                                            <input type="file" id="image_ar" class="form-control" name="image_ar" required>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="image_en"><?php echo e(__('admin.image')); ?> (<?php echo e(__('admin.en')); ?>)</label>
                                            <input type="file" id="image_en" class="form-control" name="image_en" required>
                                        </div>
                                    </div>

                                    
                                    <div class="col-md-12 col-12">
                                        <div class="form-group">
                                            <label for="is_active"><?php echo e(__('admin.status')); ?></label>
                                            <select name="is_active" class="form-control">
                                                <option value="1"><?php echo e(__('admin.active')); ?></option>
                                                <option value="0"><?php echo e(__('admin.inactive')); ?></option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.add')); ?></button>
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</form>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    
    
        <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
        <?php echo $__env->make('admin.shared.submitAddForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/images/create.blade.php ENDPATH**/ ?>