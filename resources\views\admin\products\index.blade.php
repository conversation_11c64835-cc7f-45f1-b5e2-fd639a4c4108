@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/index_page.css')}}">
@endsection

@section('content')

<x-admin.table
    datefilter="true"
    order="true"
    extrabuttons="true"
    addbutton="{{ route('admin.products.create') }}"
    deletebutton="{{ route('admin.products.deleteAll') }}"
    :searchArray="[
        'name' => [
            'input_type' => 'text' ,
            'input_name' => __('admin.product_name') ,
        ] ,
        'provider_id' => [
            'input_type' => 'select' ,
            'input_name' => __('admin.product_provider') ,
            'rows' => collect([['id' => '', 'name' => __('admin.all')]])->concat(
                \App\Models\Provider::with('user')->get()->map(function($provider) {
                    return ['id' => $provider->id, 'name' => $provider->user->name ?? 'No Name'];
                })
            )->toArray()
        ] ,
        'product_category_id' => [
            'input_type' => 'select' ,
            'input_name' => __('admin.product_category') ,
            'rows' => collect([['id' => '', 'name' => __('admin.all')]])->concat(
                \App\Models\ProductCategory::where('is_active', 1)->get()->map(function($category) {
                    return ['id' => $category->id, 'name' => $category->name];
                })
            )->toArray()
        ] ,
        'price' => [
            'input_type' => 'text' ,
            'input_name' => __('admin.product_price') ,
        ] ,
        'is_active' => [
            'input_type' => 'select' ,
            'rows' => [
                '' => [
                    'name' => __('admin.all') ,
                    'id' => '' ,
                ],
                '1' => [
                    'name' => __('admin.active') ,
                    'id' => 1 ,
                ],
                '0' => [
                    'name' => __('admin.inactive') ,
                    'id' => 0 ,
                ],
            ] ,
            'input_name' => __('admin.status') ,
        ] ,
    ]"
>

    <x-slot name="extrabuttonsdiv">
        {{-- <a type="button" data-toggle="modal" data-target="#notify" class="btn bg-gradient-info mr-1 mb-1 waves-effect waves-light notify" data-id="all"><i class="feather icon-bell"></i> {{ __('admin.Send_notification') }}</a> --}}
    </x-slot>

    <x-slot name="tableContent">
        <div class="table_content_append card">
            {{-- table content will appends here  --}}
        </div>
    </x-slot>
</x-admin.table>



@endsection

@section('js')
<script src="{{asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')}}"></script>
<script src="{{asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')}}"></script>

    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>
    @include('admin.shared.deleteAll')
    @include('admin.shared.deleteOne')
    @include('admin.shared.filter_js' , [ 'index_route' => url('admin/products')])
@endsection
