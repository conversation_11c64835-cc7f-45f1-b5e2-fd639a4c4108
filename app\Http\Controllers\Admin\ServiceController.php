<?php

namespace App\Http\Controllers\Admin;

use App\Models\Service;
use App\Models\Provider;
use App\Models\Category;
use App\Traits\Report;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Services\Store;
use App\Http\Requests\Admin\Services\Update;

class ServiceController extends Controller
{
    use Report;

    /**
     * Display a listing of the services.
     */
    public function index($id = null)
    {
        if (request()->ajax()) {
            $services = Service::with(['provider.user', 'category'])
                ->search(request()->searchArray)
                ->paginate(30);
            $html = view('admin.services.table', compact('services'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.services.index');
    }

    /**
     * Show the form for creating a new service.
     */
    public function create()
    {
        $providers = Provider::with('user')->get();

        $categories = Category::where('is_active', 1)->get();

        return view('admin.services.create', compact('providers', 'categories'));
    }

    /**
     * Store a newly created service in storage.
     */
    public function store(Store $request)
    {
        $data = $request->validated();
        $data['is_active'] = (int) $request->input('is_active', 0);

        Service::create($data);
        Report::addToLog('إضافة خدمة جديدة');
        return response()->json(['url' => route('admin.services.index')]);
    }

    /**
     * Display the specified service.
     */
    public function show($id)
    {
        $service = Service::with(['provider.user', 'category'])->findOrFail($id);
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified service.
     */
    public function edit($id)
    {
        $service = Service::findOrFail($id);

        $providers = Provider::with('user')->get();

        $categories = Category::where('is_active', 1)->get();

        return view('admin.services.edit', compact('service', 'providers', 'categories'));
    }

    /**
     * Update the specified service in storage.
     */
    public function update(Update $request, $id)
    {
        $service = Service::findOrFail($id);
        $data = $request->validated();
        $data['is_active'] = (int) $request->input('is_active', 0);

        $service->update($data);
        Report::addToLog('تعديل خدمة');
        return response()->json(['url' => route('admin.services.index')]);
    }

    /**
     * Remove the specified service from storage.
     */
    public function destroy($id)
    {
        try {
            $product = Service::findOrFail($id);

            // Check if product is related to any order items
            $orderItemsCount = $product->orderItems()->count();

            if ($orderItemsCount > 0) {
                return response()->json([
                    'error' => 'لا يمكن حذف هذا المنتج لأنه مرتبط بـ ' . $orderItemsCount . ' طلب/طلبات'
                ], 422);
            }

            $product->delete();
            Report::addToLog('حذف منتج');
            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف المنتج'], 500);
        }
    }

    /**
     * Delete multiple products
     */
    public function deleteAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        $productsWithOrders = Service::whereIn('id', $ids)
        ->whereHas('orderItems')
        ->with('orderItems')
        ->get();

    if ($productsWithOrders->count() > 0) {
        $productNames = $productsWithOrders->pluck('name')->map(function($name) {
            return is_array($name) ? ($name['ar'] ?? $name['en'] ?? 'منتج غير محدد') : $name;
        })->implode(', ');

        return response()->json([
            'error' => 'لا يمكن حذف المنتجات التالية لأنها مرتبطة بطلبات: ' . $productNames
        ], 422);
    }

        if (Service::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من المستخدمين');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }


    /**
     * Toggle service status
     */
    public function toggleStatus(Request $request)
    {
        $service = Service::findOrFail($request->id);
        $service->update(['is_active' => !$service->is_active]);

        $status = $service->is_active ? 'تفعيل' : 'إلغاء تفعيل';
        Report::addToLog($status . ' خدمة');

        return response()->json([
            'status' => $service->is_active,
            'message' => 'تم تحديث حالة الخدمة بنجاح'
        ]);
    }
}
