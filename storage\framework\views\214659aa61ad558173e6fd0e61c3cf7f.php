

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/index_page.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<?php if (isset($component)) { $__componentOriginal781089cd478f3e09d520a65f160df974 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal781089cd478f3e09d520a65f160df974 = $attributes; } ?>
<?php $component = App\View\Components\Admin\Table::resolve(['datefilter' => 'true','order' => 'true','extrabuttons' => 'true','deletebutton' => ''.e(route('admin.course_enrollments.deleteAll')).'','searchArray' => [
        'id' => [
            'input_type' => 'text' ,
            'input_name' => __('admin.enrollment_id') ,
        ] ,
        'course_id' => [
            'input_type' => 'select' ,
            'rows'       => $courses->mapWithKeys(function($course) {
                return [$course->id => ['name' => $course->name, 'id' => $course->id]];
            })->toArray() ,
            'input_name' => __('admin.course_name') ,
        ] ,
        'user_id' => [
            'input_type' => 'select' ,
            'rows'       => $users->mapWithKeys(function($user) {
                return [$user->id => ['name' => $user->name, 'id' => $user->id]];
            })->toArray() ,
            'input_name' => __('admin.client_name') ,
        ] ,
         'payment_method_id' => [
    'input_type' => 'select',
    'rows' => [
        1 => [
            'name' => __('admin.visa'),
            'id' => 1,
        ],
        2 => [
            'name' => __('admin.mada'),
            'id' => 2,
        ],
        3 => [
            'name' => __('admin.apple_pay'),
            'id' => 3,
        ],
        4 => [
            'name' => __('admin.google_pay'),
            'id' => 4,
        ],
        5 => [
            'name' => __('admin.bank_transfer'),
            'id' => 5,
        ],
        6 => [
            'name' => __('admin.wallet'),
            'id' => 6,
        ],
    ],
    'input_name' => __('admin.payment_method'),
],

        'payment_status' => [
            'input_type' => 'select' ,
            'rows'       => [
                'pending' => [
                    'name' => 'في الانتظار' ,
                    'id' => 'pending' ,
                ],
                'paid' => [
                    'name' => 'مدفوع' ,
                    'id' => 'paid' ,
                ],
                'failed' => [
                    'name' => 'فشل' ,
                    'id' => 'failed' ,
                ],
                'refunded' => [
                    'name' => 'مسترد' ,
                    'id' => 'refunded' ,
                ],
            ] ,
            'input_name' => 'حالة الدفع' ,
        ] ,
        'status' => [
            'input_type' => 'select' ,
            'rows'       => [
                'pending_payment' => [
                    'name' => 'في انتظار الدفع' ,
                    'id' => 'pending_payment' ,
                ],
                'active' => [
                    'name' => 'نشط' ,
                    'id' => 'active' ,
                ],
                'suspended' => [
                    'name' => 'معلق' ,
                    'id' => 'suspended' ,
                ],
                'completed' => [
                    'name' => 'مكتمل' ,
                    'id' => 'completed' ,
                ],
                'cancelled' => [
                    'name' => 'ملغي' ,
                    'id' => 'cancelled' ,
                ],
            ] ,
            'input_name' => 'حالة الاشتراك' ,
        ] ,
        'amount_from' => [
            'input_type' => 'number' ,
            'input_name' => 'المبلغ من' ,
        ] ,
        'amount_to' => [
            'input_type' => 'number' ,
            'input_name' => 'المبلغ إلى' ,
        ] ,
    ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Admin\Table::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>

     <?php $__env->slot('extrabuttonsdiv', null, []); ?> 
        <a class="btn bg-gradient-info mr-1 mb-1 waves-effect waves-light"  href="<?php echo e(url(route('admin.master-export', 'CourseEnrollment'))); ?>"><i  class="fa fa-file-excel-o"></i>
            <?php echo e(__('admin.export_enrollments')); ?></a>
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('tableContent', null, []); ?> 
        <div class="table_content_append card">

        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $attributes = $__attributesOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__attributesOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $component = $__componentOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__componentOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>

    
        <?php echo $__env->make('admin.shared.deleteAll', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
        <?php echo $__env->make('admin.shared.deleteOne', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
        <?php echo $__env->make('admin.shared.filter_js' , [ 'index_route' => url('admin/course-enrollments')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/course_enrollments/index.blade.php ENDPATH**/ ?>