@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css"
        href="{{ asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css') }}">
    <style>
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #dc3545;
        }
        .form-control.is-invalid, .imageUploader.is-invalid {
            border-color: #dc3545;
        }
        .form-control.is-invalid:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        /* CKEditor validation styles */
        .ck-editor.is-invalid {
            border: 2px solid #dc3545 !important;
            border-radius: 0.375rem;
        }
        .ck-editor.is-invalid .ck-editor__editable {
            border-color: #dc3545 !important;
        }
        .content-error {
            margin-top: 0.25rem;
            font-size: 0.875rem;
            color: #dc3545;
        }
    </style>
@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                {{-- <div class="card-header">
                    <h4 class="card-title">{{__('admin.add') . ' ' . __('admin.blog')}}</h4>
                </div> --}}
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="{{route('admin.blogs.store')}}" class="store form-horizontal" enctype="multipart/form-data" novalidate>
                            @csrf
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-12">
                                        <div class="col-12">
                                            <ul class="nav nav-tabs mb-3">
                                                    @foreach (languages() as $lang)
                                                        <li class="nav-item">
                                                            <a class="nav-link @if($loop->first) active @endif"  data-toggle="pill" href="#first_{{$lang}}" aria-expanded="true">{{  __('admin.data') }} {{ $lang }}</a>
                                                        </li>
                                                    @endforeach
                                            </ul>
                                        </div>

                                        <div class="col-12">
                                            <div class="imgMontg col-12 text-center">
                                                <div class="dropBox">
                                                    <div class="textCenter">
                                                        <div class="imagesUploadBlock">
                                                            <label class="uploadImg">
                                                                <span><i class="feather icon-image"></i></span>
                                                                <input type="file" accept="image/*" name="image" class="imageUploader @error('image') is-invalid @enderror">
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                @error('image')
                                                    <div class="invalid-feedback d-block text-center">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                    {{-- Language tabs content --}}
                                       <div class="tab-content">
                                                @foreach (languages() as $lang)
                                                    <div role="tabpanel" class="tab-pane fade @if($loop->first) show active @endif " id="first_{{$lang}}" aria-labelledby="first_{{$lang}}" aria-expanded="true">
                                                        <div class="row">
                                                            <div class="col-md-6 col-12">
                                                                <div class="form-group">
                                                                    <label for="title_{{$lang}}">{{__('admin.blog_title')}} {{ $lang }}</label>
                                                                    <div class="controls">
                                                                        <input type="text" name="title[{{$lang}}]" id="title_{{$lang}}" class="form-control @error('title.'.$lang) is-invalid @enderror" placeholder="{{__('admin.write') . ' ' . __('admin.blog_title')}} {{ $lang }}" value="{{ old('title.'.$lang) }}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                                        @error('title.'.$lang)
                                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                                        @enderror
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @if($loop->first)
                                                            <div class="col-md-6 col-12">
                                                                <div class="form-group">
                                                                    <label for="category_id">{{__('admin.blog_category')}}</label>
                                                                    <div class="controls">
                                                                        <select name="category_id" id="category_id" class="form-control @error('category_id') is-invalid @enderror" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                                            <option value="">{{__('admin.select_category')}}</option>
                                                                            @foreach($categories as $category)
                                                                                <option value="{{$category->id}}" {{ old('category_id') == $category->id ? 'selected' : '' }}>{{$category->name}}</option>
                                                                            @endforeach
                                                                        </select>
                                                                        @error('category_id')
                                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                                        @enderror
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @endif
                                                        </div>
                                                        @if($loop->first)
                                                        <div class="col-md-6 col-12">
                                                            <div class="form-group">
                                                                <label for="is_active">{{__('admin.status')}}</label>
                                                                <div class="controls">
                                                                    <select name="is_active" id="is_active" class="form-control @error('is_active') is-invalid @enderror" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                                        <option value="1" {{ old('is_active', '1') == '1' ? 'selected' : '' }}>{{__('admin.active')}}</option>
                                                                        <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>{{__('admin.inactive')}}</option>
                                                                    </select>
                                                                    @error('is_active')
                                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                                    @enderror
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endif
                                                        <div class="row">
                                                            <div class="col-md-12 col-12">
                                                                <div class="form-group">
                                                                     <label for="content_{{$lang}}">{{__('admin.blog_content')}} {{ $lang }}</label>
                                                                    <div class="controls">
                                                                        <textarea name="content[{{$lang}}]" id="content_{{$lang}}" class="form-control @error('content.'.$lang) is-invalid @enderror" placeholder="{{__('admin.write') . ' ' . __('admin.blog_content')}} {{ $lang }}">{{ old('content.'.$lang) }}</textarea>
                                                                        @error('content.'.$lang)
                                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                                        @enderror
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>

                                        <div class="col-12 d-flex justify-content-center mt-3">
                                            <button type="submit"
                                                class="btn btn-primary mr-1 mb-1 submit_button">{{ __('admin.add') }}</button>
                                            <a href="{{ url()->previous() }}" type="reset"
                                                class="btn btn-outline-warning mr-1 mb-1">{{ __('admin.back') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('js')
    <script src="{{ asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/forms/validation/form-validation.js') }}"></script>
    <script src="{{ asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js') }}"></script>
    <script src="{{ asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js') }}"></script>
    <!-- CKEditor 5 JS -->
    <script src="https://cdn.ckeditor.com/ckeditor5/40.2.0/classic/ckeditor.js"></script>

    {{-- show selected image script --}}
    @include('admin.shared.addImage')
    {{-- show selected image script --}}

    {{-- submit add form script --}}
    @include('admin.shared.submitAddForm')
    {{-- submit add form script --}}

    <script>
        $(document).ready(function() {
            // Store CKEditor instances
            const editorInstances = {};

            // Initialize CKEditor 5 for all content textareas
            @foreach(languages() as $lang)
                ClassicEditor
                    .create(document.querySelector('#content_{{$lang}}'), {
                        toolbar: {
                            items: [
                                'heading', '|',
                                'bold', 'italic', 'underline', 'strikethrough', '|',
                                'fontSize', 'fontColor', 'fontBackgroundColor', '|',
                                'alignment', '|',
                                'numberedList', 'bulletedList', '|',
                                'outdent', 'indent', '|',
                                'link', 'blockQuote', 'insertTable', '|',
                                'imageUpload', 'mediaEmbed', '|',
                                'undo', 'redo', '|',
                                'sourceEditing'
                            ]
                        },
                        language: '{{app()->getLocale() === "ar" ? "ar" : "en"}}',
                        image: {
                            toolbar: [
                                'imageTextAlternative',
                                'imageStyle:inline',
                                'imageStyle:block',
                                'imageStyle:side',
                                'linkImage'
                            ]
                        },
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        heading: {
                            options: [
                                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                                { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                            ]
                        },
                        fontSize: {
                            options: [
                                9, 11, 13, 'default', 17, 19, 21, 27, 35
                            ]
                        },
                        fontColor: {
                            colors: [
                                {
                                    color: 'hsl(0, 0%, 0%)',
                                    label: 'Black'
                                },
                                {
                                    color: 'hsl(0, 0%, 30%)',
                                    label: 'Dim grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 60%)',
                                    label: 'Grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 90%)',
                                    label: 'Light grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 100%)',
                                    label: 'White',
                                    hasBorder: true
                                },
                                {
                                    color: 'hsl(0, 75%, 60%)',
                                    label: 'Red'
                                },
                                {
                                    color: 'hsl(30, 75%, 60%)',
                                    label: 'Orange'
                                },
                                {
                                    color: 'hsl(60, 75%, 60%)',
                                    label: 'Yellow'
                                },
                                {
                                    color: 'hsl(90, 75%, 60%)',
                                    label: 'Light green'
                                },
                                {
                                    color: 'hsl(120, 75%, 60%)',
                                    label: 'Green'
                                },
                                {
                                    color: 'hsl(150, 75%, 60%)',
                                    label: 'Aquamarine'
                                },
                                {
                                    color: 'hsl(180, 75%, 60%)',
                                    label: 'Turquoise'
                                },
                                {
                                    color: 'hsl(210, 75%, 60%)',
                                    label: 'Light blue'
                                },
                                {
                                    color: 'hsl(240, 75%, 60%)',
                                    label: 'Blue'
                                },
                                {
                                    color: 'hsl(270, 75%, 60%)',
                                    label: 'Purple'
                                }
                            ]
                        },
                        fontBackgroundColor: {
                            colors: [
                                {
                                    color: 'hsl(0, 0%, 0%)',
                                    label: 'Black'
                                },
                                {
                                    color: 'hsl(0, 0%, 30%)',
                                    label: 'Dim grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 60%)',
                                    label: 'Grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 90%)',
                                    label: 'Light grey'
                                },
                                {
                                    color: 'hsl(0, 0%, 100%)',
                                    label: 'White',
                                    hasBorder: true
                                },
                                {
                                    color: 'hsl(0, 75%, 60%)',
                                    label: 'Red'
                                },
                                {
                                    color: 'hsl(30, 75%, 60%)',
                                    label: 'Orange'
                                },
                                {
                                    color: 'hsl(60, 75%, 60%)',
                                    label: 'Yellow'
                                },
                                {
                                    color: 'hsl(90, 75%, 60%)',
                                    label: 'Light green'
                                },
                                {
                                    color: 'hsl(120, 75%, 60%)',
                                    label: 'Green'
                                },
                                {
                                    color: 'hsl(150, 75%, 60%)',
                                    label: 'Aquamarine'
                                },
                                {
                                    color: 'hsl(180, 75%, 60%)',
                                    label: 'Turquoise'
                                },
                                {
                                    color: 'hsl(210, 75%, 60%)',
                                    label: 'Light blue'
                                },
                                {
                                    color: 'hsl(240, 75%, 60%)',
                                    label: 'Blue'
                                },
                                {
                                    color: 'hsl(270, 75%, 60%)',
                                    label: 'Purple'
                                }
                            ]
                        }
                    })
                    .then(editor => {
                        editorInstances['content_{{$lang}}'] = editor;

                        // Set minimum height
                        editor.editing.view.change(writer => {
                            writer.setStyle('min-height', '300px', editor.editing.view.document.getRoot());
                        });
                    })
                    .catch(error => {
                        console.error('CKEditor initialization error for content_{{$lang}}:', error);
                    });
            @endforeach

            // Custom validation for CKEditor 5
            $('form.store').on('submit', function(e) {
                var isValid = true;

                // Clear previous validation errors
                $('.content-error').remove();
                $('.ck-editor').removeClass('is-invalid');

                @foreach(languages() as $lang)
                    if (editorInstances['content_{{$lang}}']) {
                        var content_{{$lang}} = editorInstances['content_{{$lang}}'].getData().trim();
                        if (content_{{$lang}} === '' || content_{{$lang}} === '<p>&nbsp;</p>' || content_{{$lang}} === '<p></p>') {
                            // Show error message under the editor
                            var editorElement = $('#content_{{$lang}}').next('.ck-editor');
                            editorElement.addClass('is-invalid');
                            editorElement.after('<div class="invalid-feedback content-error d-block">{{__("admin.blog_content")}} {{$lang}} {{__("admin.this_field_is_required")}}</div>');
                            isValid = false;
                        } else {
                            // Update the textarea with editor content
                            document.querySelector('#content_{{$lang}}').value = content_{{$lang}};
                        }
                    }
                @endforeach

                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
            });

            // Clear validation errors when user starts typing in CKEditor
            @foreach(languages() as $lang)
                if (editorInstances['content_{{$lang}}']) {
                    editorInstances['content_{{$lang}}'].model.document.on('change:data', () => {
                        var editorElement = $('#content_{{$lang}}').next('.ck-editor');
                        editorElement.removeClass('is-invalid');
                        editorElement.siblings('.content-error').remove();
                        // Also clear backend validation errors
                        $('#content_{{$lang}}').siblings('.invalid-feedback').hide();
                    });
                }
            @endforeach

            // Handle backend validation errors for CKEditor
            @if($errors->any())
                // Wait for CKEditor to be fully loaded before showing errors
                setTimeout(function() {
                    @foreach(languages() as $lang)
                        @if($errors->has('content.'.$lang))
                            var editorElement = $('#content_{{$lang}}').next('.ck-editor');
                            if (editorElement.length) {
                                editorElement.addClass('is-invalid');
                                // Remove any existing error messages first
                                editorElement.siblings('.content-error').remove();
                                editorElement.after('<div class="invalid-feedback content-error d-block">{{ $errors->first('content.'.$lang) }}</div>');
                            }
                        @endif
                    @endforeach
                }, 1000); // Wait 1 second for CKEditor to load
            @endif
        });
    </script>
@endsection
